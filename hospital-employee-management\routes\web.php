<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\DepartmentController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\LeaveController;
use App\Http\Controllers\AttendanceController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/quick-stats', [DashboardController::class, 'quickStats'])->name('dashboard.quick-stats');

    // Employee Management
    Route::resource('employees', EmployeeController::class);
    Route::get('/my-profile', [EmployeeController::class, 'profile'])->name('employees.profile');
    Route::patch('/employees/{employee}/toggle-status', [EmployeeController::class, 'toggleStatus'])->name('employees.toggle-status');

    // Department Management
    Route::resource('departments', DepartmentController::class);
    Route::patch('/departments/{department}/toggle-status', [DepartmentController::class, 'toggleStatus'])->name('departments.toggle-status');
    Route::get('/departments/{department}/employees', [DepartmentController::class, 'employees'])->name('departments.employees');
    Route::get('/departments/{department}/statistics', [DepartmentController::class, 'statistics'])->name('departments.statistics');

    // Position Management
    Route::resource('positions', PositionController::class);
    Route::patch('/positions/{position}/toggle-status', [PositionController::class, 'toggleStatus'])->name('positions.toggle-status');
    Route::get('/departments/{department}/positions', [PositionController::class, 'byDepartment'])->name('positions.by-department');
    Route::get('/positions/{position}/details', [PositionController::class, 'details'])->name('positions.details');

    // Leave Management
    Route::resource('leaves', LeaveController::class);
    Route::patch('/leaves/{leave}/approve', [LeaveController::class, 'approve'])->name('leaves.approve');
    Route::patch('/leaves/{leave}/reject', [LeaveController::class, 'reject'])->name('leaves.reject');
    Route::patch('/leaves/{leave}/cancel', [LeaveController::class, 'cancel'])->name('leaves.cancel');
    Route::get('/leaves/{leave}/download-document', [LeaveController::class, 'downloadDocument'])->name('leaves.download-document');

    // Attendance Management
    Route::resource('attendance', AttendanceController::class)->only(['index', 'show', 'edit', 'update']);
    Route::get('/clock', [AttendanceController::class, 'clockInterface'])->name('attendance.clock');
    Route::post('/clock/{action}', [AttendanceController::class, 'clockAction'])->name('attendance.clock-action')
        ->where('action', 'clock-in|clock-out|break-start|break-end');
    Route::patch('/attendance/{attendance}/approve', [AttendanceController::class, 'approve'])->name('attendance.approve');
    Route::get('/attendance/summary/{employee?}', [AttendanceController::class, 'summary'])->name('attendance.summary');
    Route::get('/attendance/export', [AttendanceController::class, 'export'])->name('attendance.export');

    // Profile Management
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
