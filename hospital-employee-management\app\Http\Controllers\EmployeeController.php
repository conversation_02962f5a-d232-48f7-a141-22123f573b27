<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreEmployeeRequest;
use App\Http\Requests\UpdateEmployeeRequest;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Position;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Role;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view employees');

        $query = Employee::with(['user', 'department', 'position', 'supervisor'])
            ->when($request->search, function ($q) use ($request) {
                $q->where(function ($query) use ($request) {
                    $query->where('first_name', 'like', "%{$request->search}%")
                          ->orWhere('last_name', 'like', "%{$request->search}%")
                          ->orWhere('employee_id', 'like', "%{$request->search}%")
                          ->orWhereHas('user', function ($q) use ($request) {
                              $q->where('email', 'like', "%{$request->search}%");
                          });
                });
            })
            ->when($request->department, function ($q) use ($request) {
                $q->where('department_id', $request->department);
            })
            ->when($request->position, function ($q) use ($request) {
                $q->where('position_id', $request->position);
            })
            ->when($request->status, function ($q) use ($request) {
                $q->where('employment_status', $request->status);
            });

        // Apply department-based filtering for department heads
        if ($request->user()->isDepartmentHead() && !$request->user()->can('view employees')) {
            $query->where('department_id', $request->user()->employee->department_id);
        }

        $employees = $query->latest()->paginate(15);

        $departments = Department::active()->get();
        $positions = Position::active()->get();

        return view('employees.index', compact('employees', 'departments', 'positions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create employees');

        $departments = Department::active()->get();
        $positions = Position::active()->get();
        $supervisors = Employee::active()->get();
        $roles = Role::all();

        return view('employees.create', compact('departments', 'positions', 'supervisors', 'roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreEmployeeRequest $request)
    {
        DB::beginTransaction();

        try {
            // Create user account
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            // Assign role
            $user->assignRole($request->role);

            // Handle profile photo upload
            $profilePhotoPath = null;
            if ($request->hasFile('profile_photo')) {
                $profilePhotoPath = $request->file('profile_photo')->store('employee-photos', 'public');
            }

            // Create employee profile
            $employeeData = $request->validated();
            $employeeData['user_id'] = $user->id;
            $employeeData['profile_photo'] = $profilePhotoPath;
            
            // Remove user-related fields from employee data
            unset($employeeData['name'], $employeeData['email'], $employeeData['password'], 
                  $employeeData['password_confirmation'], $employeeData['role']);

            $employee = Employee::create($employeeData);

            DB::commit();

            return redirect()->route('employees.show', $employee)
                           ->with('success', 'Employee created successfully.');

        } catch (\Exception $e) {
            DB::rollback();
            
            // Delete uploaded photo if exists
            if (isset($profilePhotoPath) && Storage::disk('public')->exists($profilePhotoPath)) {
                Storage::disk('public')->delete($profilePhotoPath);
            }

            return back()->withInput()
                        ->with('error', 'Failed to create employee. Please try again.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Employee $employee)
    {
        $this->authorize('view employee profiles');

        // Check if user can view this specific employee
        if (!auth()->user()->can('view employees') && 
            auth()->user()->employee->id !== $employee->id &&
            auth()->user()->employee->department_id !== $employee->department_id) {
            abort(403, 'You can only view employees in your department.');
        }

        $employee->load([
            'user.roles',
            'department',
            'position',
            'supervisor',
            'subordinates',
            'emergencyContacts',
            'documents' => function ($query) {
                $query->latest();
            }
        ]);

        return view('employees.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Employee $employee)
    {
        $this->authorize('edit employees');

        // Check if user can edit this specific employee
        if (!auth()->user()->can('edit employees') && 
            auth()->user()->employee->id !== $employee->id) {
            abort(403, 'You can only edit your own profile.');
        }

        $employee->load('user.roles');
        $departments = Department::active()->get();
        $positions = Position::active()->get();
        $supervisors = Employee::active()->where('id', '!=', $employee->id)->get();
        $roles = Role::all();

        return view('employees.edit', compact('employee', 'departments', 'positions', 'supervisors', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateEmployeeRequest $request, Employee $employee)
    {
        DB::beginTransaction();

        try {
            $validatedData = $request->validated();

            // Update user account if data provided
            if (isset($validatedData['name']) || isset($validatedData['email']) || isset($validatedData['password'])) {
                $userData = [];
                
                if (isset($validatedData['name'])) {
                    $userData['name'] = $validatedData['name'];
                }
                
                if (isset($validatedData['email'])) {
                    $userData['email'] = $validatedData['email'];
                }
                
                if (isset($validatedData['password'])) {
                    $userData['password'] = Hash::make($validatedData['password']);
                }

                $employee->user->update($userData);

                // Update role if provided and user has permission
                if (isset($validatedData['role']) && auth()->user()->can('manage users')) {
                    $employee->user->syncRoles([$validatedData['role']]);
                }
            }

            // Handle profile photo upload
            if ($request->hasFile('profile_photo')) {
                // Delete old photo
                if ($employee->profile_photo && Storage::disk('public')->exists($employee->profile_photo)) {
                    Storage::disk('public')->delete($employee->profile_photo);
                }
                
                $validatedData['profile_photo'] = $request->file('profile_photo')->store('employee-photos', 'public');
            }

            // Remove user-related fields from employee data
            unset($validatedData['name'], $validatedData['email'], $validatedData['password'], 
                  $validatedData['password_confirmation'], $validatedData['role']);

            // Update employee profile
            $employee->update($validatedData);

            DB::commit();

            return redirect()->route('employees.show', $employee)
                           ->with('success', 'Employee updated successfully.');

        } catch (\Exception $e) {
            DB::rollback();

            return back()->withInput()
                        ->with('error', 'Failed to update employee. Please try again.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Employee $employee)
    {
        $this->authorize('delete employees');

        // Check if employee can be deleted (no active relationships)
        if ($employee->subordinates()->count() > 0) {
            return back()->with('error', 'Cannot delete employee who supervises other employees.');
        }

        if ($employee->attendances()->count() > 0 || $employee->leaves()->count() > 0) {
            return back()->with('error', 'Cannot delete employee with attendance or leave records.');
        }

        DB::beginTransaction();

        try {
            // Delete profile photo
            if ($employee->profile_photo && Storage::disk('public')->exists($employee->profile_photo)) {
                Storage::disk('public')->delete($employee->profile_photo);
            }

            // Delete employee and user account
            $user = $employee->user;
            $employee->delete();
            $user->delete();

            DB::commit();

            return redirect()->route('employees.index')
                           ->with('success', 'Employee deleted successfully.');

        } catch (\Exception $e) {
            DB::rollback();

            return back()->with('error', 'Failed to delete employee. Please try again.');
        }
    }

    /**
     * Show employee profile (for self-viewing)
     */
    public function profile()
    {
        $employee = auth()->user()->employee;
        
        if (!$employee) {
            abort(404, 'Employee profile not found.');
        }

        $employee->load([
            'user.roles',
            'department',
            'position',
            'supervisor',
            'emergencyContacts',
            'documents' => function ($query) {
                $query->latest();
            }
        ]);

        return view('employees.profile', compact('employee'));
    }

    /**
     * Toggle employee status
     */
    public function toggleStatus(Employee $employee)
    {
        $this->authorize('edit employees');

        $newStatus = $employee->employment_status === 'active' ? 'inactive' : 'active';
        
        $employee->update(['employment_status' => $newStatus]);

        return back()->with('success', "Employee status changed to {$newStatus}.");
    }
}
