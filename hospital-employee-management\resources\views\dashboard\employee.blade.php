<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('My Dashboard') }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">Welcome back, {{ $employee->first_name }}!</p>
            </div>
            <div class="text-sm text-gray-600">
                {{ now()->format('l, F j, Y') }}
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Personal Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <x-stats-card 
                    title="Years of Service" 
                    :value="$stats['years_of_service']"
                    color="blue"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\' clip-rule=\'evenodd\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Leave Balance" 
                    :value="$stats['leave_balance'] . ' days'"
                    color="green"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path d=\'M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="This Month Attendance" 
                    :value="$stats['attendance_this_month'] . ' days'"
                    color="purple"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\' clip-rule=\'evenodd\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Pending Requests" 
                    :value="$stats['pending_leaves']"
                    color="yellow"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\' clip-rule=\'evenodd\'></path></svg>'"
                />
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Today's Status -->
                <x-card title="Today's Status" class="lg:col-span-1">
                    <div class="space-y-4">
                        <!-- Clock Status -->
                        <div class="p-4 bg-gray-50 rounded-lg">
                            <h4 class="font-medium text-gray-900 mb-2">Attendance</h4>
                            @if($todayAttendance)
                                <div class="space-y-2">
                                    @if($todayAttendance->clock_in)
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Clock In:</span>
                                            <span class="font-medium">{{ $todayAttendance->clock_in->format('H:i') }}</span>
                                        </div>
                                    @endif
                                    
                                    @if($todayAttendance->clock_out)
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Clock Out:</span>
                                            <span class="font-medium">{{ $todayAttendance->clock_out->format('H:i') }}</span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-gray-600">Total Hours:</span>
                                            <span class="font-medium">{{ $todayAttendance->total_hours ?? 'N/A' }}</span>
                                        </div>
                                    @else
                                        <x-badge type="success" size="sm">Currently Working</x-badge>
                                    @endif
                                </div>
                            @else
                                <p class="text-gray-500 text-sm">Not clocked in today</p>
                            @endif
                        </div>

                        <!-- Schedule -->
                        @if($todaySchedule)
                            <div class="p-4 bg-blue-50 rounded-lg">
                                <h4 class="font-medium text-gray-900 mb-2">Today's Schedule</h4>
                                <div class="space-y-1 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Start:</span>
                                        <span class="font-medium">{{ $todaySchedule->start_time }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">End:</span>
                                        <span class="font-medium">{{ $todaySchedule->end_time }}</span>
                                    </div>
                                    @if($todaySchedule->break_duration)
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Break:</span>
                                            <span class="font-medium">{{ $todaySchedule->break_duration }} min</span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                    
                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('attendance.clock') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            Go to Clock Interface →
                        </a>
                    </div>
                </x-card>

                <!-- Recent Leaves -->
                <x-card title="Recent Leave Requests" class="lg:col-span-1">
                    <div class="space-y-3">
                        @forelse($recentLeaves as $leave)
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="font-medium text-gray-900">{{ $leave->leaveType->name }}</p>
                                        <p class="text-sm text-gray-600">
                                            {{ $leave->start_date->format('M j') }} - {{ $leave->end_date->format('M j, Y') }}
                                        </p>
                                        <p class="text-xs text-gray-500">{{ $leave->days_requested }} days</p>
                                    </div>
                                    <x-badge 
                                        :type="$leave->status === 'approved' ? 'success' : ($leave->status === 'rejected' ? 'danger' : 'warning')"
                                        size="xs"
                                    >
                                        {{ ucfirst($leave->status) }}
                                    </x-badge>
                                </div>
                                @if($leave->reason)
                                    <p class="text-xs text-gray-500 mt-2">{{ Str::limit($leave->reason, 60) }}</p>
                                @endif
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No recent leave requests</p>
                        @endforelse
                    </div>
                    
                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('leaves.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            View all requests →
                        </a>
                    </div>
                </x-card>

                <!-- Upcoming Leaves -->
                <x-card title="Upcoming Leaves" class="lg:col-span-1">
                    <div class="space-y-3">
                        @forelse($upcomingLeaves as $leave)
                            <div class="p-3 bg-green-50 rounded-lg">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="font-medium text-gray-900">{{ $leave->leaveType->name }}</p>
                                        <p class="text-sm text-gray-600">
                                            {{ $leave->start_date->format('M j') }} - {{ $leave->end_date->format('M j, Y') }}
                                        </p>
                                        <p class="text-xs text-gray-500">{{ $leave->days_requested }} days</p>
                                        <p class="text-xs text-green-600 font-medium">
                                            Starts in {{ $leave->start_date->diffForHumans() }}
                                        </p>
                                    </div>
                                    <x-badge type="success" size="xs">Approved</x-badge>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No upcoming leaves</p>
                        @endforelse
                    </div>
                    
                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('leaves.create') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            Request new leave →
                        </a>
                    </div>
                </x-card>
            </div>

            <!-- Recent Attendance -->
            <x-card title="Recent Attendance" subtitle="Your attendance for the past 7 days">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock In</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock Out</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Break</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($recentAttendance as $attendance)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $attendance->date->format('M j, Y') }}
                                        <br>
                                        <span class="text-xs text-gray-500">{{ $attendance->date->format('l') }}</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $attendance->clock_in ? $attendance->clock_in->format('H:i') : '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $attendance->clock_out ? $attendance->clock_out->format('H:i') : '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if($attendance->break_start && $attendance->break_end)
                                            {{ $attendance->break_start->format('H:i') }} - {{ $attendance->break_end->format('H:i') }}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $attendance->total_hours ?? '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <x-badge 
                                            :type="$attendance->status === 'present' ? 'success' : ($attendance->status === 'late' ? 'warning' : 'danger')"
                                            size="xs"
                                        >
                                            {{ ucfirst($attendance->status) }}
                                        </x-badge>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        No attendance records found
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </x-card>

            <!-- Quick Actions -->
            <x-card title="Quick Actions">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <a href="{{ route('attendance.clock') }}" class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                        <svg class="w-8 h-8 text-blue-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm font-medium text-blue-900">Clock In/Out</span>
                    </a>
                    
                    @can('request leave')
                        <a href="{{ route('leaves.create') }}" class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                            <svg class="w-8 h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-sm font-medium text-green-900">Request Leave</span>
                        </a>
                    @endcan
                    
                    <a href="{{ route('employees.profile') }}" class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                        <svg class="w-8 h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span class="text-sm font-medium text-purple-900">My Profile</span>
                    </a>
                    
                    <a href="{{ route('leaves.index') }}" class="flex flex-col items-center p-4 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors">
                        <svg class="w-8 h-8 text-yellow-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <span class="text-sm font-medium text-yellow-900">My Requests</span>
                    </a>
                </div>
            </x-card>
        </div>
    </div>
</x-app-layout>
