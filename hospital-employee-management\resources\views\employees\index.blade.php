<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Employees') }}
            </h2>
            @can('create employees')
                <a href="{{ route('employees.create') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Add Employee
                </a>
            @endcan
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            
            <!-- Breadcrumb -->
            <x-breadcrumb :items="[
                ['title' => 'Dashboard', 'url' => route('dashboard')],
                ['title' => 'Employees']
            ]" />

            <!-- Filters -->
            <x-card class="mb-6">
                <form method="GET" action="{{ route('employees.index') }}" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                    <div class="flex-1">
                        <x-input-label for="search" value="Search" />
                        <x-text-input 
                            id="search" 
                            name="search" 
                            type="text" 
                            :value="request('search')"
                            placeholder="Search by name, employee ID, or email..."
                            class="mt-1 block w-full"
                        />
                    </div>
                    
                    <div class="w-full md:w-48">
                        <x-input-label for="department" value="Department" />
                        <x-select 
                            id="department" 
                            name="department" 
                            :value="request('department')"
                            placeholder="All Departments"
                            class="mt-1 block w-full"
                        >
                            @foreach($departments as $department)
                                <option value="{{ $department->id }}" {{ request('department') == $department->id ? 'selected' : '' }}>
                                    {{ $department->name }}
                                </option>
                            @endforeach
                        </x-select>
                    </div>
                    
                    <div class="w-full md:w-48">
                        <x-input-label for="position" value="Position" />
                        <x-select 
                            id="position" 
                            name="position" 
                            :value="request('position')"
                            placeholder="All Positions"
                            class="mt-1 block w-full"
                        >
                            @foreach($positions as $position)
                                <option value="{{ $position->id }}" {{ request('position') == $position->id ? 'selected' : '' }}>
                                    {{ $position->title }}
                                </option>
                            @endforeach
                        </x-select>
                    </div>
                    
                    <div class="w-full md:w-32">
                        <x-input-label for="status" value="Status" />
                        <x-select 
                            id="status" 
                            name="status" 
                            :value="request('status')"
                            placeholder="All Status"
                            class="mt-1 block w-full"
                        >
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </x-select>
                    </div>
                    
                    <div class="flex space-x-2">
                        <x-primary-button type="submit">
                            Filter
                        </x-primary-button>
                        
                        @if(request()->hasAny(['search', 'department', 'position', 'status']))
                            <a href="{{ route('employees.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                Clear
                            </a>
                        @endif
                    </div>
                </form>
            </x-card>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <x-alert type="success" dismissible class="mb-6">
                    {{ session('success') }}
                </x-alert>
            @endif

            @if(session('error'))
                <x-alert type="error" dismissible class="mb-6">
                    {{ session('error') }}
                </x-alert>
            @endif

            <!-- Employees Table -->
            <x-card>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Employee
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Department
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Position
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Contact
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($employees as $employee)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <span class="text-sm font-medium text-gray-700">
                                                        {{ substr($employee->first_name, 0, 1) }}{{ substr($employee->last_name, 0, 1) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $employee->full_name }}
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    ID: {{ $employee->employee_id }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $employee->department->name ?? 'N/A' }}</div>
                                        @if($employee->supervisor)
                                            <div class="text-sm text-gray-500">Reports to: {{ $employee->supervisor->full_name }}</div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $employee->position->title ?? 'N/A' }}</div>
                                        @if($employee->position && $employee->position->level)
                                            <div class="text-sm text-gray-500">Level {{ $employee->position->level }}</div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $employee->user->email }}</div>
                                        @if($employee->phone)
                                            <div class="text-sm text-gray-500">{{ $employee->phone }}</div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <x-badge :type="$employee->employment_status === 'active' ? 'success' : 'warning'">
                                            {{ ucfirst($employee->employment_status) }}
                                        </x-badge>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        @can('view employee profiles')
                                            <a href="{{ route('employees.show', $employee) }}" class="text-indigo-600 hover:text-indigo-900">
                                                View
                                            </a>
                                        @endcan
                                        
                                        @can('edit employees')
                                            <a href="{{ route('employees.edit', $employee) }}" class="text-green-600 hover:text-green-900">
                                                Edit
                                            </a>
                                        @endcan
                                        
                                        @can('edit employees')
                                            <form method="POST" action="{{ route('employees.toggle-status', $employee) }}" class="inline">
                                                @csrf
                                                @method('PATCH')
                                                <button type="submit" class="text-yellow-600 hover:text-yellow-900" 
                                                        onclick="return confirm('Are you sure you want to change this employee\'s status?')">
                                                    {{ $employee->employment_status === 'active' ? 'Deactivate' : 'Activate' }}
                                                </button>
                                            </form>
                                        @endcan
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                        No employees found.
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($employees->hasPages())
                    <div class="px-6 py-4 border-t border-gray-200">
                        {{ $employees->withQueryString()->links() }}
                    </div>
                @endif
            </x-card>
        </div>
    </div>
</x-app-layout>
