<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'code',
        'head_id',
        'location',
        'phone',
        'email',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the department head (employee who heads this department)
     */
    public function head(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'head_id');
    }

    /**
     * Get all employees in this department
     */
    public function employees(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Get active employees in this department
     */
    public function activeEmployees(): HasMany
    {
        return $this->hasMany(Employee::class)->where('employment_status', 'active');
    }

    /**
     * Get positions available in this department
     */
    public function positions(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Position::class);
    }

    /**
     * Scope to get only active departments
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the total number of employees in this department
     */
    public function getEmployeeCountAttribute(): int
    {
        return $this->employees()->count();
    }

    /**
     * Get the total number of active employees in this department
     */
    public function getActiveEmployeeCountAttribute(): int
    {
        return $this->activeEmployees()->count();
    }

    /**
     * Check if department can be deleted (no active employees)
     */
    public function canBeDeleted(): bool
    {
        return $this->activeEmployees()->count() === 0;
    }
}
