<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('edit employees') || 
               ($this->user()->can('edit employee profiles') && $this->user()->employee->id === $this->route('employee')->id);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $employee = $this->route('employee');
        
        return [
            // User account information
            'name' => ['sometimes', 'required', 'string', 'max:255'],
            'email' => ['sometimes', 'required', 'string', 'email', 'max:255', 
                       Rule::unique('users')->ignore($employee->user_id)],
            'password' => ['nullable', 'string', 'min:8', 'confirmed'],
            
            // Employee basic information
            'employee_id' => ['sometimes', 'required', 'string', 'max:20', 
                             Rule::unique('employees')->ignore($employee->id)],
            'department_id' => ['sometimes', 'required', 'exists:departments,id'],
            'position_id' => ['sometimes', 'required', 'exists:positions,id'],
            'supervisor_id' => ['nullable', 'exists:employees,id', 
                               Rule::notIn([$employee->id])], // Can't supervise themselves
            
            // Personal information
            'first_name' => ['sometimes', 'required', 'string', 'max:255'],
            'middle_name' => ['nullable', 'string', 'max:255'],
            'last_name' => ['sometimes', 'required', 'string', 'max:255'],
            'date_of_birth' => ['sometimes', 'required', 'date', 'before:today'],
            'gender' => ['sometimes', 'required', Rule::in(['male', 'female', 'other'])],
            'marital_status' => ['sometimes', 'required', Rule::in(['single', 'married', 'divorced', 'widowed'])],
            'nationality' => ['sometimes', 'required', 'string', 'max:255'],
            'id_number' => ['sometimes', 'required', 'string', 'max:20', 
                           Rule::unique('employees')->ignore($employee->id)],
            
            // Contact information
            'phone' => ['sometimes', 'required', 'string', 'max:20'],
            'personal_email' => ['nullable', 'email', 'max:255'],
            'address' => ['sometimes', 'required', 'string'],
            'city' => ['sometimes', 'required', 'string', 'max:255'],
            'postal_code' => ['sometimes', 'required', 'string', 'max:10'],
            
            // Employment information
            'hire_date' => ['sometimes', 'required', 'date'],
            'probation_end_date' => ['nullable', 'date', 'after:hire_date'],
            'employment_type' => ['sometimes', 'required', Rule::in(['full_time', 'part_time', 'contract', 'intern'])],
            'employment_status' => ['sometimes', 'required', Rule::in(['active', 'inactive', 'terminated', 'resigned'])],
            'current_salary' => ['sometimes', 'required', 'numeric', 'min:0'],
            'work_location' => ['nullable', 'string', 'max:255'],
            
            // Medical information (optional for medical staff)
            'medical_license_number' => ['nullable', 'string', 'max:255'],
            'medical_license_expiry' => ['nullable', 'date', 'after:today'],
            'certifications' => ['nullable', 'array'],
            'certifications.*' => ['string', 'max:255'],
            'specializations' => ['nullable', 'array'],
            'specializations.*' => ['string', 'max:255'],
            
            // Banking information
            'bank_account_number' => ['nullable', 'string', 'max:255'],
            'bank_name' => ['nullable', 'string', 'max:255'],
            'tax_id' => ['nullable', 'string', 'max:255'],
            
            // Additional information
            'profile_photo' => ['nullable', 'image', 'mimes:jpeg,png,jpg', 'max:2048'],
            'notes' => ['nullable', 'string'],
            
            // Role assignment (only for users with permission)
            'role' => [
                Rule::requiredIf($this->user()->can('manage users')),
                'exists:roles,name'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'employee_id.unique' => 'This employee ID is already taken.',
            'id_number.unique' => 'This ID number is already registered.',
            'email.unique' => 'This email address is already registered.',
            'date_of_birth.before' => 'Date of birth must be before today.',
            'probation_end_date.after' => 'Probation end date must be after hire date.',
            'medical_license_expiry.after' => 'Medical license expiry must be in the future.',
            'current_salary.min' => 'Salary must be a positive number.',
            'profile_photo.image' => 'Profile photo must be an image file.',
            'profile_photo.max' => 'Profile photo must not exceed 2MB.',
            'supervisor_id.not_in' => 'An employee cannot supervise themselves.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee ID',
            'department_id' => 'department',
            'position_id' => 'position',
            'supervisor_id' => 'supervisor',
            'first_name' => 'first name',
            'middle_name' => 'middle name',
            'last_name' => 'last name',
            'date_of_birth' => 'date of birth',
            'marital_status' => 'marital status',
            'id_number' => 'ID number',
            'personal_email' => 'personal email',
            'postal_code' => 'postal code',
            'hire_date' => 'hire date',
            'probation_end_date' => 'probation end date',
            'employment_type' => 'employment type',
            'employment_status' => 'employment status',
            'current_salary' => 'current salary',
            'work_location' => 'work location',
            'medical_license_number' => 'medical license number',
            'medical_license_expiry' => 'medical license expiry',
            'bank_account_number' => 'bank account number',
            'bank_name' => 'bank name',
            'tax_id' => 'tax ID',
            'profile_photo' => 'profile photo',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert arrays to proper format if they come as strings
        if ($this->has('certifications') && is_string($this->certifications)) {
            $this->merge([
                'certifications' => array_filter(explode(',', $this->certifications))
            ]);
        }

        if ($this->has('specializations') && is_string($this->specializations)) {
            $this->merge([
                'specializations' => array_filter(explode(',', $this->specializations))
            ]);
        }

        // Remove password if empty (don't update password)
        if ($this->has('password') && empty($this->password)) {
            $this->request->remove('password');
            $this->request->remove('password_confirmation');
        }
    }
}
