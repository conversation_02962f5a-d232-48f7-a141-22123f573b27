<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Leave extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'leave_type_id',
        'start_date',
        'end_date',
        'days_requested',
        'reason',
        'status',
        'applied_at',
        'approved_by',
        'approved_at',
        'rejected_reason',
        'document_path',
        'emergency_contact',
        'handover_notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'days_requested' => 'integer',
        'applied_at' => 'datetime',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the employee who requested this leave
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the leave type
     */
    public function leaveType(): BelongsTo
    {
        return $this->belongsTo(LeaveType::class);
    }

    /**
     * Get the approver of this leave request
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * Scope to get pending leave requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get approved leave requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to get rejected leave requests
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope to get cancelled leave requests
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Scope to get leave requests for a specific date range
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('start_date', [$startDate, $endDate])
              ->orWhereBetween('end_date', [$startDate, $endDate])
              ->orWhere(function ($q2) use ($startDate, $endDate) {
                  $q2->where('start_date', '<=', $startDate)
                     ->where('end_date', '>=', $endDate);
              });
        });
    }

    /**
     * Scope to get current leave requests (ongoing)
     */
    public function scopeCurrent($query)
    {
        $today = Carbon::today();
        return $query->where('status', 'approved')
                    ->where('start_date', '<=', $today)
                    ->where('end_date', '>=', $today);
    }

    /**
     * Scope to get upcoming leave requests
     */
    public function scopeUpcoming($query)
    {
        return $query->where('status', 'approved')
                    ->where('start_date', '>', Carbon::today());
    }

    /**
     * Calculate the number of days between start and end date
     */
    public function calculateDays(): int
    {
        if (!$this->start_date || !$this->end_date) {
            return 0;
        }

        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Calculate working days (excluding weekends)
     */
    public function calculateWorkingDays(): int
    {
        if (!$this->start_date || !$this->end_date) {
            return 0;
        }

        $workingDays = 0;
        $current = $this->start_date->copy();

        while ($current->lte($this->end_date)) {
            if (!$current->isWeekend()) {
                $workingDays++;
            }
            $current->addDay();
        }

        return $workingDays;
    }

    /**
     * Check if leave is currently active
     */
    public function isActive(): bool
    {
        if ($this->status !== 'approved') {
            return false;
        }

        $today = Carbon::today();
        return $this->start_date->lte($today) && $this->end_date->gte($today);
    }

    /**
     * Check if leave is upcoming
     */
    public function isUpcoming(): bool
    {
        return $this->status === 'approved' && $this->start_date->gt(Carbon::today());
    }

    /**
     * Check if leave is in the past
     */
    public function isPast(): bool
    {
        return $this->end_date->lt(Carbon::today());
    }

    /**
     * Check if leave can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'approved']) && 
               $this->start_date->gt(Carbon::today());
    }

    /**
     * Check if leave overlaps with another leave
     */
    public function overlapsWith(Leave $otherLeave): bool
    {
        if ($this->employee_id !== $otherLeave->employee_id) {
            return false;
        }

        return $this->start_date->lte($otherLeave->end_date) && 
               $this->end_date->gte($otherLeave->start_date);
    }

    /**
     * Get the duration in a human readable format
     */
    public function getDurationAttribute(): string
    {
        $days = $this->days_requested ?: $this->calculateDays();
        
        if ($days === 1) {
            return '1 day';
        }
        
        return $days . ' days';
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'cancelled' => 'secondary',
            default => 'primary'
        };
    }

    /**
     * Auto-calculate days when saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($leave) {
            if ($leave->start_date && $leave->end_date && !$leave->days_requested) {
                $leave->days_requested = $leave->calculateWorkingDays();
            }
        });
    }
}
