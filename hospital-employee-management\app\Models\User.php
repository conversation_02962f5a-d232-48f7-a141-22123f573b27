<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_active',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Get the employee profile associated with this user
     */
    public function employee(): HasOne
    {
        return $this->hasOne(Employee::class);
    }

    /**
     * Scope to get only active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if user has employee profile
     */
    public function hasEmployeeProfile(): bool
    {
        return $this->employee()->exists();
    }

    /**
     * Get user's full name from employee profile or fallback to name
     */
    public function getFullNameAttribute(): string
    {
        if ($this->hasEmployeeProfile()) {
            return $this->employee->full_name;
        }

        return $this->name;
    }

    /**
     * Check if user is a super admin
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole('Super Admin');
    }

    /**
     * Check if user is an HR manager
     */
    public function isHRManager(): bool
    {
        return $this->hasRole('HR Manager');
    }

    /**
     * Check if user is a department head
     */
    public function isDepartmentHead(): bool
    {
        return $this->hasRole('Department Head');
    }

    /**
     * Check if user is a regular employee
     */
    public function isEmployee(): bool
    {
        return $this->hasRole('Employee');
    }
}
