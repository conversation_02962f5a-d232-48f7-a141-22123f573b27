<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;

class Employee extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'user_id',
        'department_id',
        'position_id',
        'supervisor_id',
        'first_name',
        'middle_name',
        'last_name',
        'date_of_birth',
        'gender',
        'marital_status',
        'nationality',
        'id_number',
        'phone',
        'personal_email',
        'address',
        'city',
        'postal_code',
        'hire_date',
        'probation_end_date',
        'employment_type',
        'employment_status',
        'current_salary',
        'work_location',
        'medical_license_number',
        'medical_license_expiry',
        'certifications',
        'specializations',
        'bank_account_number',
        'bank_name',
        'tax_id',
        'profile_photo',
        'notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'hire_date' => 'date',
        'probation_end_date' => 'date',
        'medical_license_expiry' => 'date',
        'current_salary' => 'decimal:2',
        'certifications' => 'array',
        'specializations' => 'array',
    ];

    /**
     * Get the user account associated with this employee
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the department this employee belongs to
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the position of this employee
     */
    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    /**
     * Get the supervisor of this employee
     */
    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'supervisor_id');
    }

    /**
     * Get employees supervised by this employee
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(Employee::class, 'supervisor_id');
    }

    /**
     * Get emergency contacts for this employee
     */
    public function emergencyContacts(): HasMany
    {
        return $this->hasMany(EmployeeEmergencyContact::class);
    }

    /**
     * Get schedules for this employee
     */
    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    /**
     * Get attendance records for this employee
     */
    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get leave requests for this employee
     */
    public function leaves(): HasMany
    {
        return $this->hasMany(Leave::class);
    }

    /**
     * Get evaluations for this employee
     */
    public function evaluations(): HasMany
    {
        return $this->hasMany(Evaluation::class);
    }

    /**
     * Get payroll records for this employee
     */
    public function payrolls(): HasMany
    {
        return $this->hasMany(Payroll::class);
    }

    /**
     * Get documents for this employee
     */
    public function documents(): HasMany
    {
        return $this->hasMany(EmployeeDocument::class);
    }

    /**
     * Scope to get only active employees
     */
    public function scopeActive($query)
    {
        return $query->where('employment_status', 'active');
    }

    /**
     * Scope to filter by department
     */
    public function scopeInDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * Scope to filter by position
     */
    public function scopeInPosition($query, $positionId)
    {
        return $query->where('position_id', $positionId);
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        $name = $this->first_name;
        if ($this->middle_name) {
            $name .= ' ' . $this->middle_name;
        }
        $name .= ' ' . $this->last_name;
        return $name;
    }

    /**
     * Get age attribute
     */
    public function getAgeAttribute(): int
    {
        return Carbon::parse($this->date_of_birth)->age;
    }

    /**
     * Get years of service attribute
     */
    public function getYearsOfServiceAttribute(): float
    {
        return Carbon::parse($this->hire_date)->diffInYears(Carbon::now(), true);
    }

    /**
     * Check if employee is still in probation period
     */
    public function isInProbation(): bool
    {
        return $this->probation_end_date && Carbon::now()->lt($this->probation_end_date);
    }

    /**
     * Check if medical license is expired or expiring soon
     */
    public function isMedicalLicenseExpiring(int $days = 30): bool
    {
        if (!$this->medical_license_expiry) {
            return false;
        }
        
        return Carbon::now()->addDays($days)->gte($this->medical_license_expiry);
    }

    /**
     * Get formatted salary
     */
    public function getFormattedSalaryAttribute(): string
    {
        return 'Rp ' . number_format($this->current_salary, 0, ',', '.');
    }
}
