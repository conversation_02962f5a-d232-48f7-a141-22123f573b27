<?php

namespace App\Http\Controllers;

use App\Http\Requests\ClockInOutRequest;
use App\Models\Attendance;
use App\Models\Schedule;
use App\Models\Employee;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view attendance');

        $query = Attendance::with(['employee.user', 'employee.department', 'schedule', 'approver'])
            ->when($request->search, function ($q) use ($request) {
                $q->whereHas('employee', function ($query) use ($request) {
                    $query->where('first_name', 'like', "%{$request->search}%")
                          ->orWhere('last_name', 'like', "%{$request->search}%")
                          ->orWhere('employee_id', 'like', "%{$request->search}%");
                });
            })
            ->when($request->status, function ($q) use ($request) {
                $q->where('status', $request->status);
            })
            ->when($request->department, function ($q) use ($request) {
                $q->whereHas('employee', function ($query) use ($request) {
                    $query->where('department_id', $request->department);
                });
            })
            ->when($request->date_from, function ($q) use ($request) {
                $q->where('date', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($q) use ($request) {
                $q->where('date', '<=', $request->date_to);
            });

        // Apply department-based filtering for department heads
        if ($request->user()->isDepartmentHead() && !$request->user()->can('view attendance')) {
            $query->whereHas('employee', function ($q) use ($request) {
                $q->where('department_id', $request->user()->employee->department_id);
            });
        }

        // Show only own attendance for regular employees
        if ($request->user()->isEmployee() && !$request->user()->can('view attendance')) {
            $query->where('employee_id', $request->user()->employee->id);
        }

        $attendances = $query->latest('date')->latest('clock_in')->paginate(15);

        $departments = \App\Models\Department::active()->get();

        return view('attendance.index', compact('attendances', 'departments'));
    }

    /**
     * Show the clock in/out interface
     */
    public function clockInterface()
    {
        $this->authorize('clock in/out');

        $employee = auth()->user()->employee;
        $today = Carbon::today();

        // Get today's attendance
        $todayAttendance = Attendance::where('employee_id', $employee->id)
            ->where('date', $today)
            ->first();

        // Get today's schedule
        $todaySchedule = Schedule::where('employee_id', $employee->id)
            ->where('date', $today)
            ->where('status', 'active')
            ->first();

        // Get recent attendance (last 7 days)
        $recentAttendance = Attendance::where('employee_id', $employee->id)
            ->where('date', '>=', $today->copy()->subDays(7))
            ->where('date', '<', $today)
            ->with('schedule')
            ->orderBy('date', 'desc')
            ->get();

        return view('attendance.clock', compact('todayAttendance', 'todaySchedule', 'recentAttendance'));
    }

    /**
     * Handle clock in/out actions
     */
    public function clockAction(ClockInOutRequest $request, $action)
    {
        $employee = auth()->user()->employee;
        $today = Carbon::today();
        $now = now();

        // Get or create today's attendance record
        $attendance = Attendance::firstOrCreate(
            [
                'employee_id' => $employee->id,
                'date' => $today,
            ],
            [
                'status' => 'present',
            ]
        );

        // Get today's schedule if available
        $schedule = Schedule::where('employee_id', $employee->id)
            ->where('date', $today)
            ->where('status', 'active')
            ->first();

        if ($schedule) {
            $attendance->schedule_id = $schedule->id;
        }

        switch ($action) {
            case 'clock-in':
                $attendance->clock_in = $now;
                $attendance->clock_in_location = $request->clock_in_location;
                break;

            case 'clock-out':
                $attendance->clock_out = $now;
                $attendance->clock_out_location = $request->clock_out_location;
                
                // Calculate attendance metrics
                $attendance->updateCalculatedFields();
                break;

            case 'break-start':
                $attendance->break_start = $now;
                break;

            case 'break-end':
                $attendance->break_end = $now;
                
                // Recalculate total hours if clocked out
                if ($attendance->clock_out) {
                    $attendance->updateCalculatedFields();
                }
                break;
        }

        if ($request->notes) {
            $attendance->notes = $request->notes;
        }

        $attendance->save();

        $messages = [
            'clock-in' => 'Clocked in successfully.',
            'clock-out' => 'Clocked out successfully.',
            'break-start' => 'Break started.',
            'break-end' => 'Break ended.',
        ];

        return back()->with('success', $messages[$action]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Attendance $attendance)
    {
        $this->authorize('view own attendance');

        // Check if user can view this specific attendance
        if (!auth()->user()->can('view attendance') && 
            auth()->user()->employee->id !== $attendance->employee_id &&
            auth()->user()->employee->department_id !== $attendance->employee->department_id) {
            abort(403, 'You can only view attendance in your department.');
        }

        $attendance->load(['employee.user', 'employee.department', 'schedule', 'approver']);

        return view('attendance.show', compact('attendance'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Attendance $attendance)
    {
        $this->authorize('edit attendance');

        $attendance->load(['employee.user', 'schedule']);
        $schedules = Schedule::where('employee_id', $attendance->employee_id)
            ->where('date', $attendance->date)
            ->get();

        return view('attendance.edit', compact('attendance', 'schedules'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Attendance $attendance)
    {
        $this->authorize('edit attendance');

        $request->validate([
            'clock_in' => 'nullable|date',
            'clock_out' => 'nullable|date|after:clock_in',
            'break_start' => 'nullable|date|after:clock_in|before:clock_out',
            'break_end' => 'nullable|date|after:break_start|before:clock_out',
            'schedule_id' => 'nullable|exists:schedules,id',
            'status' => 'required|in:present,absent,late,half_day,overtime',
            'notes' => 'nullable|string|max:500',
        ]);

        $attendance->update($request->only([
            'clock_in', 'clock_out', 'break_start', 'break_end', 
            'schedule_id', 'status', 'notes'
        ]));

        // Recalculate attendance metrics
        $attendance->updateCalculatedFields();
        $attendance->save();

        return redirect()->route('attendance.show', $attendance)
                        ->with('success', 'Attendance updated successfully.');
    }

    /**
     * Approve attendance record
     */
    public function approve(Attendance $attendance)
    {
        $this->authorize('approve attendance');

        if ($attendance->status === 'approved') {
            return back()->with('error', 'Attendance is already approved.');
        }

        $attendance->update([
            'status' => 'approved',
            'approved_by' => auth()->user()->employee->id,
            'approved_at' => now(),
        ]);

        return back()->with('success', 'Attendance approved successfully.');
    }

    /**
     * Get attendance summary for employee
     */
    public function summary(Request $request, Employee $employee = null)
    {
        $this->authorize('view attendance');

        $employee = $employee ?: auth()->user()->employee;

        // Check permissions
        if (!auth()->user()->can('view attendance') && 
            auth()->user()->employee->id !== $employee->id &&
            auth()->user()->employee->department_id !== $employee->department_id) {
            abort(403, 'You can only view attendance in your department.');
        }

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->startOfMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now()->endOfMonth();

        $attendances = Attendance::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->with('schedule')
            ->orderBy('date')
            ->get();

        $summary = [
            'total_days' => $attendances->count(),
            'present_days' => $attendances->where('status', 'present')->count(),
            'absent_days' => $attendances->where('status', 'absent')->count(),
            'late_days' => $attendances->where('late_minutes', '>', 0)->count(),
            'total_hours' => $attendances->sum('total_hours'),
            'overtime_hours' => $attendances->sum('overtime_hours'),
            'average_hours' => $attendances->avg('total_hours'),
        ];

        return view('attendance.summary', compact('employee', 'attendances', 'summary', 'startDate', 'endDate'));
    }

    /**
     * Export attendance data
     */
    public function export(Request $request)
    {
        $this->authorize('view reports');

        // Implementation for exporting attendance data
        // This would typically use Laravel Excel or similar package
        
        return back()->with('info', 'Export functionality will be implemented.');
    }
}
