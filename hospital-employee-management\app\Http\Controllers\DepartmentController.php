<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreDepartmentRequest;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Http\Request;

class DepartmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view departments');

        $query = Department::with(['head', 'employees' => function ($q) {
            $q->where('employment_status', 'active');
        }])
        ->withCount(['employees', 'activeEmployees'])
        ->when($request->search, function ($q) use ($request) {
            $q->where('name', 'like', "%{$request->search}%")
              ->orWhere('code', 'like', "%{$request->search}%");
        })
        ->when($request->has('status'), function ($q) use ($request) {
            $q->where('is_active', $request->status === 'active');
        });

        $departments = $query->latest()->paginate(15);

        return view('departments.index', compact('departments'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create departments');

        $employees = Employee::active()
            ->with(['position', 'department'])
            ->get()
            ->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'name' => $employee->full_name,
                    'position' => $employee->position->title ?? 'N/A',
                    'department' => $employee->department->name ?? 'N/A',
                ];
            });

        return view('departments.create', compact('employees'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDepartmentRequest $request)
    {
        $department = Department::create($request->validated());

        return redirect()->route('departments.show', $department)
                        ->with('success', 'Department created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Department $department)
    {
        $this->authorize('view departments');

        $department->load([
            'head.position',
            'employees.position',
            'positions' => function ($query) {
                $query->active()->withCount('activeEmployees');
            }
        ]);

        // Get department statistics
        $stats = [
            'total_employees' => $department->employees()->count(),
            'active_employees' => $department->activeEmployees()->count(),
            'total_positions' => $department->positions()->count(),
            'active_positions' => $department->positions()->active()->count(),
        ];

        // Get recent employees (last 30 days)
        $recentEmployees = $department->employees()
            ->where('hire_date', '>=', now()->subDays(30))
            ->with('position')
            ->latest('hire_date')
            ->limit(5)
            ->get();

        return view('departments.show', compact('department', 'stats', 'recentEmployees'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Department $department)
    {
        $this->authorize('edit departments');

        $employees = Employee::active()
            ->with(['position', 'department'])
            ->get()
            ->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'name' => $employee->full_name,
                    'position' => $employee->position->title ?? 'N/A',
                    'department' => $employee->department->name ?? 'N/A',
                ];
            });

        return view('departments.edit', compact('department', 'employees'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StoreDepartmentRequest $request, Department $department)
    {
        $department->update($request->validated());

        return redirect()->route('departments.show', $department)
                        ->with('success', 'Department updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Department $department)
    {
        $this->authorize('delete departments');

        // Check if department can be deleted
        if (!$department->canBeDeleted()) {
            return back()->with('error', 'Cannot delete department with active employees.');
        }

        $department->delete();

        return redirect()->route('departments.index')
                        ->with('success', 'Department deleted successfully.');
    }

    /**
     * Toggle department status
     */
    public function toggleStatus(Department $department)
    {
        $this->authorize('edit departments');

        $department->update(['is_active' => !$department->is_active]);

        $status = $department->is_active ? 'activated' : 'deactivated';
        
        return back()->with('success', "Department {$status} successfully.");
    }

    /**
     * Get department employees (AJAX)
     */
    public function employees(Department $department)
    {
        $this->authorize('view departments');

        $employees = $department->employees()
            ->with(['position', 'user'])
            ->where('employment_status', 'active')
            ->get()
            ->map(function ($employee) {
                return [
                    'id' => $employee->id,
                    'employee_id' => $employee->employee_id,
                    'name' => $employee->full_name,
                    'position' => $employee->position->title,
                    'email' => $employee->user->email,
                    'phone' => $employee->phone,
                    'hire_date' => $employee->hire_date->format('Y-m-d'),
                ];
            });

        return response()->json($employees);
    }

    /**
     * Get department statistics (AJAX)
     */
    public function statistics(Department $department)
    {
        $this->authorize('view departments');

        $stats = [
            'total_employees' => $department->employees()->count(),
            'active_employees' => $department->activeEmployees()->count(),
            'inactive_employees' => $department->employees()->where('employment_status', '!=', 'active')->count(),
            'total_positions' => $department->positions()->count(),
            'filled_positions' => $department->positions()->whereHas('employees', function ($q) {
                $q->where('employment_status', 'active');
            })->count(),
            'vacant_positions' => $department->positions()->whereDoesntHave('employees', function ($q) {
                $q->where('employment_status', 'active');
            })->count(),
        ];

        return response()->json($stats);
    }
}
