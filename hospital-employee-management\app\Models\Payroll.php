<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payroll extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'pay_period_start',
        'pay_period_end',
        'basic_salary',
        'overtime_hours',
        'overtime_rate',
        'overtime_pay',
        'allowances',
        'bonuses',
        'gross_pay',
        'tax_deduction',
        'insurance_deduction',
        'other_deductions',
        'total_deductions',
        'net_pay',
        'status',
        'processed_by',
        'processed_at',
        'paid_at',
        'payment_method',
        'notes',
    ];

    protected $casts = [
        'pay_period_start' => 'date',
        'pay_period_end' => 'date',
        'basic_salary' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'overtime_rate' => 'decimal:2',
        'overtime_pay' => 'decimal:2',
        'allowances' => 'array',
        'bonuses' => 'array',
        'gross_pay' => 'decimal:2',
        'tax_deduction' => 'decimal:2',
        'insurance_deduction' => 'decimal:2',
        'other_deductions' => 'array',
        'total_deductions' => 'decimal:2',
        'net_pay' => 'decimal:2',
        'processed_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the employee this payroll belongs to
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the processor of this payroll
     */
    public function processor(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'processed_by');
    }

    /**
     * Scope to get payroll by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending payrolls
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get processed payrolls
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', 'processed');
    }

    /**
     * Scope to get paid payrolls
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to get payrolls for a specific period
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->where('pay_period_start', '>=', $startDate)
                    ->where('pay_period_end', '<=', $endDate);
    }

    /**
     * Calculate total allowances
     */
    public function calculateTotalAllowances(): float
    {
        if (!$this->allowances || empty($this->allowances)) {
            return 0;
        }

        return array_sum(array_values($this->allowances));
    }

    /**
     * Calculate total bonuses
     */
    public function calculateTotalBonuses(): float
    {
        if (!$this->bonuses || empty($this->bonuses)) {
            return 0;
        }

        return array_sum(array_values($this->bonuses));
    }

    /**
     * Calculate total other deductions
     */
    public function calculateTotalOtherDeductions(): float
    {
        if (!$this->other_deductions || empty($this->other_deductions)) {
            return 0;
        }

        return array_sum(array_values($this->other_deductions));
    }

    /**
     * Calculate gross pay
     */
    public function calculateGrossPay(): float
    {
        return $this->basic_salary + 
               $this->overtime_pay + 
               $this->calculateTotalAllowances() + 
               $this->calculateTotalBonuses();
    }

    /**
     * Calculate total deductions
     */
    public function calculateTotalDeductions(): float
    {
        return $this->tax_deduction + 
               $this->insurance_deduction + 
               $this->calculateTotalOtherDeductions();
    }

    /**
     * Calculate net pay
     */
    public function calculateNetPay(): float
    {
        return $this->calculateGrossPay() - $this->calculateTotalDeductions();
    }

    /**
     * Get pay period in human readable format
     */
    public function getPayPeriodAttribute(): string
    {
        return $this->pay_period_start->format('M d') . ' - ' . 
               $this->pay_period_end->format('M d, Y');
    }

    /**
     * Get formatted basic salary
     */
    public function getFormattedBasicSalaryAttribute(): string
    {
        return 'Rp ' . number_format($this->basic_salary, 0, ',', '.');
    }

    /**
     * Get formatted gross pay
     */
    public function getFormattedGrossPayAttribute(): string
    {
        return 'Rp ' . number_format($this->gross_pay, 0, ',', '.');
    }

    /**
     * Get formatted net pay
     */
    public function getFormattedNetPayAttribute(): string
    {
        return 'Rp ' . number_format($this->net_pay, 0, ',', '.');
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'processed' => 'info',
            'paid' => 'success',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Auto-calculate payroll amounts when saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($payroll) {
            // Calculate overtime pay if not set
            if ($payroll->overtime_hours && $payroll->overtime_rate && !$payroll->overtime_pay) {
                $payroll->overtime_pay = $payroll->overtime_hours * $payroll->overtime_rate;
            }

            // Calculate gross pay if not set
            if (!$payroll->gross_pay) {
                $payroll->gross_pay = $payroll->calculateGrossPay();
            }

            // Calculate total deductions if not set
            if (!$payroll->total_deductions) {
                $payroll->total_deductions = $payroll->calculateTotalDeductions();
            }

            // Calculate net pay if not set
            if (!$payroll->net_pay) {
                $payroll->net_pay = $payroll->calculateNetPay();
            }
        });
    }
}
