<?php

namespace App\Http\Requests;

use App\Models\Leave;
use App\Models\LeaveType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class StoreLeaveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('request leave') || $this->user()->can('create leaves');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => [
                'required_if:can_create_for_others,true',
                'exists:employees,id'
            ],
            'leave_type_id' => ['required', 'exists:leave_types,id'],
            'start_date' => ['required', 'date', 'after_or_equal:today'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date'],
            'reason' => ['required', 'string', 'max:1000'],
            'emergency_contact' => ['nullable', 'string', 'max:255'],
            'handover_notes' => ['nullable', 'string', 'max:1000'],
            'document' => [
                Rule::requiredIf(function () {
                    if ($this->leave_type_id) {
                        $leaveType = LeaveType::find($this->leave_type_id);
                        return $leaveType && $leaveType->requires_document;
                    }
                    return false;
                }),
                'file',
                'mimes:pdf,doc,docx,jpg,jpeg,png',
                'max:5120' // 5MB
            ],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check for overlapping leaves
            if ($this->start_date && $this->end_date && $this->employee_id) {
                $employeeId = $this->employee_id ?: $this->user()->employee->id;
                
                $overlappingLeave = Leave::where('employee_id', $employeeId)
                    ->where('status', '!=', 'rejected')
                    ->where('status', '!=', 'cancelled')
                    ->where(function ($query) {
                        $query->whereBetween('start_date', [$this->start_date, $this->end_date])
                              ->orWhereBetween('end_date', [$this->start_date, $this->end_date])
                              ->orWhere(function ($q) {
                                  $q->where('start_date', '<=', $this->start_date)
                                    ->where('end_date', '>=', $this->end_date);
                              });
                    })
                    ->exists();

                if ($overlappingLeave) {
                    $validator->errors()->add('start_date', 'You already have a leave request for this period.');
                }
            }

            // Check leave balance/quota
            if ($this->leave_type_id && $this->start_date && $this->end_date) {
                $leaveType = LeaveType::find($this->leave_type_id);
                $employeeId = $this->employee_id ?: $this->user()->employee->id;
                
                if ($leaveType && $leaveType->max_days_per_year) {
                    $currentYear = Carbon::parse($this->start_date)->year;
                    $usedDays = Leave::where('employee_id', $employeeId)
                        ->where('leave_type_id', $this->leave_type_id)
                        ->where('status', 'approved')
                        ->whereYear('start_date', $currentYear)
                        ->sum('days_requested');

                    $requestedDays = Carbon::parse($this->start_date)
                        ->diffInDays(Carbon::parse($this->end_date)) + 1;

                    if (($usedDays + $requestedDays) > $leaveType->max_days_per_year) {
                        $remainingDays = $leaveType->max_days_per_year - $usedDays;
                        $validator->errors()->add('end_date', 
                            "You only have {$remainingDays} days remaining for this leave type this year.");
                    }
                }
            }

            // Validate minimum advance notice (e.g., 3 days for annual leave)
            if ($this->start_date && $this->leave_type_id) {
                $leaveType = LeaveType::find($this->leave_type_id);
                $startDate = Carbon::parse($this->start_date);
                $today = Carbon::today();

                // Require 3 days advance notice for annual leave
                if ($leaveType && $leaveType->name === 'Annual Leave' && $startDate->diffInDays($today) < 3) {
                    $validator->errors()->add('start_date', 
                        'Annual leave must be requested at least 3 days in advance.');
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'leave_type_id.required' => 'Please select a leave type.',
            'leave_type_id.exists' => 'The selected leave type is invalid.',
            'start_date.required' => 'Please specify the start date.',
            'start_date.after_or_equal' => 'Leave cannot be requested for past dates.',
            'end_date.required' => 'Please specify the end date.',
            'end_date.after_or_equal' => 'End date must be on or after the start date.',
            'reason.required' => 'Please provide a reason for the leave.',
            'reason.max' => 'Reason cannot exceed 1000 characters.',
            'document.required' => 'A supporting document is required for this leave type.',
            'document.file' => 'Please upload a valid file.',
            'document.mimes' => 'Document must be a PDF, Word document, or image file.',
            'document.max' => 'Document size cannot exceed 5MB.',
            'emergency_contact.max' => 'Emergency contact cannot exceed 255 characters.',
            'handover_notes.max' => 'Handover notes cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'leave_type_id' => 'leave type',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'emergency_contact' => 'emergency contact',
            'handover_notes' => 'handover notes',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set employee_id to current user's employee if not provided and user can't create for others
        if (!$this->has('employee_id') || !$this->user()->can('create leaves')) {
            $this->merge([
                'employee_id' => $this->user()->employee->id ?? null,
                'can_create_for_others' => $this->user()->can('create leaves')
            ]);
        } else {
            $this->merge([
                'can_create_for_others' => true
            ]);
        }

        // Set applied_at timestamp
        $this->merge([
            'applied_at' => now()
        ]);
    }
}
