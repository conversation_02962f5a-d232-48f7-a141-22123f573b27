@props(['options' => [], 'placeholder' => 'Select an option', 'disabled' => false])

<select @disabled($disabled) {{ $attributes->merge(['class' => 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm']) }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    
    @foreach($options as $value => $label)
        <option value="{{ $value }}" {{ old($attributes->get('name'), $attributes->get('value')) == $value ? 'selected' : '' }}>
            {{ $label }}
        </option>
    @endforeach
    
    {{ $slot }}
</select>
