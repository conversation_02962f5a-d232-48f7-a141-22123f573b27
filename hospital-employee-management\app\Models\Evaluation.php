<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Evaluation extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'evaluator_id',
        'evaluation_period_start',
        'evaluation_period_end',
        'type',
        'overall_rating',
        'goals_achievements',
        'strengths',
        'areas_for_improvement',
        'development_plan',
        'evaluator_comments',
        'employee_comments',
        'status',
        'submitted_at',
        'reviewed_at',
        'criteria_scores',
    ];

    protected $casts = [
        'evaluation_period_start' => 'date',
        'evaluation_period_end' => 'date',
        'overall_rating' => 'decimal:2',
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'criteria_scores' => 'array',
    ];

    /**
     * Get the employee being evaluated
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the evaluator (supervisor/manager)
     */
    public function evaluator(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'evaluator_id');
    }

    /**
     * Scope to get evaluations by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get evaluations by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending evaluations
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get completed evaluations
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get evaluations for a specific period
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->where('evaluation_period_start', '>=', $startDate)
                    ->where('evaluation_period_end', '<=', $endDate);
    }

    /**
     * Calculate average score from criteria scores
     */
    public function calculateAverageScore(): float
    {
        if (!$this->criteria_scores || empty($this->criteria_scores)) {
            return 0;
        }

        $scores = array_values($this->criteria_scores);
        return round(array_sum($scores) / count($scores), 2);
    }

    /**
     * Get performance level based on overall rating
     */
    public function getPerformanceLevelAttribute(): string
    {
        $rating = $this->overall_rating;
        
        if ($rating >= 4.5) {
            return 'Exceptional';
        } elseif ($rating >= 3.5) {
            return 'Exceeds Expectations';
        } elseif ($rating >= 2.5) {
            return 'Meets Expectations';
        } elseif ($rating >= 1.5) {
            return 'Below Expectations';
        } else {
            return 'Unsatisfactory';
        }
    }

    /**
     * Get performance color for UI
     */
    public function getPerformanceColorAttribute(): string
    {
        $rating = $this->overall_rating;
        
        if ($rating >= 4.5) {
            return 'success';
        } elseif ($rating >= 3.5) {
            return 'info';
        } elseif ($rating >= 2.5) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    /**
     * Check if evaluation is overdue
     */
    public function isOverdue(): bool
    {
        return $this->status === 'pending' && 
               $this->evaluation_period_end->addDays(30)->isPast();
    }

    /**
     * Get the evaluation period in human readable format
     */
    public function getEvaluationPeriodAttribute(): string
    {
        return $this->evaluation_period_start->format('M Y') . ' - ' . 
               $this->evaluation_period_end->format('M Y');
    }

    /**
     * Auto-calculate overall rating when saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($evaluation) {
            if ($evaluation->criteria_scores && !$evaluation->overall_rating) {
                $evaluation->overall_rating = $evaluation->calculateAverageScore();
            }
        });
    }
}
