<?php

namespace Database\Seeders;

use App\Models\Department;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $departments = [
            [
                'name' => 'Emergency Department',
                'description' => 'Provides immediate medical care for urgent and life-threatening conditions',
                'code' => 'ED',
                'location' => 'Ground Floor, Wing A',
                'phone' => '+62-21-1234-5001',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Internal Medicine',
                'description' => 'Diagnosis and treatment of adult diseases',
                'code' => 'IM',
                'location' => '2nd Floor, Wing B',
                'phone' => '+62-21-1234-5002',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Surgery',
                'description' => 'Surgical procedures and post-operative care',
                'code' => 'SURG',
                'location' => '3rd Floor, Wing C',
                'phone' => '+62-21-1234-5003',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Pediatrics',
                'description' => 'Medical care for infants, children, and adolescents',
                'code' => 'PED',
                'location' => '2nd Floor, Wing A',
                'phone' => '+62-21-1234-5004',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Obstetrics and Gynecology',
                'description' => 'Women\'s health, pregnancy, and childbirth care',
                'code' => 'OBGYN',
                'location' => '4th Floor, Wing B',
                'phone' => '+62-21-1234-5005',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Cardiology',
                'description' => 'Heart and cardiovascular system care',
                'code' => 'CARD',
                'location' => '5th Floor, Wing A',
                'phone' => '+62-21-1234-5006',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Orthopedics',
                'description' => 'Musculoskeletal system treatment',
                'code' => 'ORTHO',
                'location' => '3rd Floor, Wing A',
                'phone' => '+62-21-1234-5007',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Radiology',
                'description' => 'Medical imaging and diagnostic services',
                'code' => 'RAD',
                'location' => 'Basement, Wing B',
                'phone' => '+62-21-1234-5008',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Laboratory',
                'description' => 'Clinical laboratory testing and analysis',
                'code' => 'LAB',
                'location' => 'Basement, Wing A',
                'phone' => '+62-21-1234-5009',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Pharmacy',
                'description' => 'Medication dispensing and pharmaceutical care',
                'code' => 'PHARM',
                'location' => 'Ground Floor, Wing B',
                'phone' => '+62-21-1234-5010',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Nursing',
                'description' => 'Patient care and nursing services',
                'code' => 'NURS',
                'location' => 'All Floors',
                'phone' => '+62-21-1234-5011',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Administration',
                'description' => 'Hospital administration and management',
                'code' => 'ADMIN',
                'location' => '1st Floor, Wing C',
                'phone' => '+62-21-1234-5012',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Human Resources',
                'description' => 'Employee management and HR services',
                'code' => 'HR',
                'location' => '1st Floor, Wing C',
                'phone' => '+62-21-1234-5013',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Information Technology',
                'description' => 'IT support and system management',
                'code' => 'IT',
                'location' => 'Basement, Wing C',
                'phone' => '+62-21-1234-5014',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
            [
                'name' => 'Maintenance',
                'description' => 'Facility maintenance and engineering',
                'code' => 'MAINT',
                'location' => 'Basement, Wing C',
                'phone' => '+62-21-1234-5015',
                'email' => '<EMAIL>',
                'is_active' => true,
            ],
        ];

        foreach ($departments as $department) {
            Department::create($department);
        }

        $this->command->info('Departments seeded successfully!');
    }
}
