<?php

namespace App\Http\Requests;

use App\Models\Attendance;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class ClockInOutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('clock in/out');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $action = $this->route('action'); // 'clock-in', 'clock-out', 'break-start', 'break-end'
        
        $rules = [
            'location' => ['nullable', 'string', 'max:255'],
            'notes' => ['nullable', 'string', 'max:500'],
        ];

        // Add specific rules based on action
        switch ($action) {
            case 'clock-in':
                $rules['schedule_id'] = ['nullable', 'exists:schedules,id'];
                break;
                
            case 'clock-out':
                // No additional rules needed
                break;
                
            case 'break-start':
            case 'break-end':
                // No additional rules needed
                break;
        }

        return $rules;
    }

    /**
     * Configure the validator instance.
     */
    public function withV<PERSON>dator($validator): void
    {
        $validator->after(function ($validator) {
            $action = $this->route('action');
            $employee = $this->user()->employee;
            $today = Carbon::today();

            if (!$employee) {
                $validator->errors()->add('employee', 'No employee profile found for this user.');
                return;
            }

            // Get today's attendance record
            $attendance = Attendance::where('employee_id', $employee->id)
                ->where('date', $today)
                ->first();

            switch ($action) {
                case 'clock-in':
                    // Check if already clocked in today
                    if ($attendance && $attendance->clock_in && !$attendance->clock_out) {
                        $validator->errors()->add('clock_in', 'You are already clocked in for today.');
                    }
                    
                    // Check if trying to clock in again after clocking out
                    if ($attendance && $attendance->clock_in && $attendance->clock_out) {
                        $validator->errors()->add('clock_in', 'You have already completed your shift for today.');
                    }
                    break;

                case 'clock-out':
                    // Check if not clocked in
                    if (!$attendance || !$attendance->clock_in) {
                        $validator->errors()->add('clock_out', 'You must clock in first before clocking out.');
                    }
                    
                    // Check if already clocked out
                    if ($attendance && $attendance->clock_out) {
                        $validator->errors()->add('clock_out', 'You have already clocked out for today.');
                    }
                    
                    // Check if on break
                    if ($attendance && $attendance->break_start && !$attendance->break_end) {
                        $validator->errors()->add('clock_out', 'You must end your break before clocking out.');
                    }
                    break;

                case 'break-start':
                    // Check if not clocked in
                    if (!$attendance || !$attendance->clock_in) {
                        $validator->errors()->add('break_start', 'You must clock in first before starting a break.');
                    }
                    
                    // Check if already clocked out
                    if ($attendance && $attendance->clock_out) {
                        $validator->errors()->add('break_start', 'You cannot start a break after clocking out.');
                    }
                    
                    // Check if already on break
                    if ($attendance && $attendance->break_start && !$attendance->break_end) {
                        $validator->errors()->add('break_start', 'You are already on break.');
                    }
                    break;

                case 'break-end':
                    // Check if not on break
                    if (!$attendance || !$attendance->break_start || $attendance->break_end) {
                        $validator->errors()->add('break_end', 'You are not currently on break.');
                    }
                    break;
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'location.max' => 'Location cannot exceed 255 characters.',
            'notes.max' => 'Notes cannot exceed 500 characters.',
            'schedule_id.exists' => 'The selected schedule is invalid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'schedule_id' => 'schedule',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $action = $this->route('action');
        
        // Add timestamp and employee information
        $this->merge([
            'employee_id' => $this->user()->employee->id ?? null,
            'date' => Carbon::today(),
            'timestamp' => now(),
            'action' => $action,
        ]);

        // Set location field name based on action
        if ($this->has('location')) {
            switch ($action) {
                case 'clock-in':
                    $this->merge(['clock_in_location' => $this->location]);
                    break;
                case 'clock-out':
                    $this->merge(['clock_out_location' => $this->location]);
                    break;
            }
        }
    }
}
