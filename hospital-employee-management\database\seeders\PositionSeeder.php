<?php

namespace Database\Seeders;

use App\Models\Position;
use App\Models\Department;
use Illuminate\Database\Seeder;

class PositionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get departments for reference
        $departments = Department::all()->keyBy('code');

        $positions = [
            // Emergency Department
            [
                'title' => 'Emergency Medicine Physician',
                'description' => 'Provides emergency medical care and treatment',
                'department_id' => $departments['ED']->id,
                'level' => 'Senior',
                'min_salary' => 25000000,
                'max_salary' => 40000000,
                'requirements' => ['Medical degree', 'Emergency medicine specialization', 'Valid medical license'],
                'responsibilities' => ['Emergency patient care', 'Trauma management', 'Critical decision making'],
                'is_active' => true,
            ],
            [
                'title' => 'Emergency Nurse',
                'description' => 'Provides nursing care in emergency situations',
                'department_id' => $departments['ED']->id,
                'level' => 'Mid',
                'min_salary' => 8000000,
                'max_salary' => 15000000,
                'requirements' => ['Nursing degree', 'Emergency nursing certification', 'BLS/ACLS certification'],
                'responsibilities' => ['Patient assessment', 'Emergency procedures', 'Patient monitoring'],
                'is_active' => true,
            ],

            // Internal Medicine
            [
                'title' => 'Internal Medicine Physician',
                'description' => 'Diagnoses and treats adult diseases',
                'department_id' => $departments['IM']->id,
                'level' => 'Senior',
                'min_salary' => 20000000,
                'max_salary' => 35000000,
                'requirements' => ['Medical degree', 'Internal medicine specialization', 'Board certification'],
                'responsibilities' => ['Patient diagnosis', 'Treatment planning', 'Chronic disease management'],
                'is_active' => true,
            ],
            [
                'title' => 'Resident Physician',
                'description' => 'Medical resident in training',
                'department_id' => $departments['IM']->id,
                'level' => 'Junior',
                'min_salary' => 8000000,
                'max_salary' => 12000000,
                'requirements' => ['Medical degree', 'Residency program enrollment'],
                'responsibilities' => ['Patient care under supervision', 'Medical procedures', 'Documentation'],
                'is_active' => true,
            ],

            // Surgery
            [
                'title' => 'Surgeon',
                'description' => 'Performs surgical procedures',
                'department_id' => $departments['SURG']->id,
                'level' => 'Senior',
                'min_salary' => 30000000,
                'max_salary' => 50000000,
                'requirements' => ['Medical degree', 'Surgery specialization', 'Board certification'],
                'responsibilities' => ['Surgical procedures', 'Pre/post-operative care', 'Surgical planning'],
                'is_active' => true,
            ],
            [
                'title' => 'Operating Room Nurse',
                'description' => 'Assists in surgical procedures',
                'department_id' => $departments['SURG']->id,
                'level' => 'Mid',
                'min_salary' => 10000000,
                'max_salary' => 18000000,
                'requirements' => ['Nursing degree', 'OR certification', 'Sterile technique training'],
                'responsibilities' => ['Surgical assistance', 'Equipment preparation', 'Patient safety'],
                'is_active' => true,
            ],

            // Pediatrics
            [
                'title' => 'Pediatrician',
                'description' => 'Provides medical care for children',
                'department_id' => $departments['PED']->id,
                'level' => 'Senior',
                'min_salary' => 22000000,
                'max_salary' => 38000000,
                'requirements' => ['Medical degree', 'Pediatrics specialization', 'Board certification'],
                'responsibilities' => ['Child healthcare', 'Developmental assessment', 'Family counseling'],
                'is_active' => true,
            ],
            [
                'title' => 'Pediatric Nurse',
                'description' => 'Provides nursing care for children',
                'department_id' => $departments['PED']->id,
                'level' => 'Mid',
                'min_salary' => 8500000,
                'max_salary' => 16000000,
                'requirements' => ['Nursing degree', 'Pediatric nursing certification'],
                'responsibilities' => ['Child patient care', 'Family support', 'Medication administration'],
                'is_active' => true,
            ],

            // Nursing
            [
                'title' => 'Head Nurse',
                'description' => 'Supervises nursing staff and operations',
                'department_id' => $departments['NURS']->id,
                'level' => 'Senior',
                'min_salary' => 15000000,
                'max_salary' => 25000000,
                'requirements' => ['Nursing degree', 'Management experience', 'Leadership certification'],
                'responsibilities' => ['Staff supervision', 'Quality assurance', 'Policy implementation'],
                'is_active' => true,
            ],
            [
                'title' => 'Staff Nurse',
                'description' => 'Provides direct patient care',
                'department_id' => $departments['NURS']->id,
                'level' => 'Mid',
                'min_salary' => 7000000,
                'max_salary' => 14000000,
                'requirements' => ['Nursing degree', 'Valid nursing license'],
                'responsibilities' => ['Patient care', 'Medication administration', 'Documentation'],
                'is_active' => true,
            ],

            // Administration
            [
                'title' => 'Hospital Administrator',
                'description' => 'Manages hospital operations',
                'department_id' => $departments['ADMIN']->id,
                'level' => 'Executive',
                'min_salary' => 35000000,
                'max_salary' => 60000000,
                'requirements' => ['Healthcare administration degree', 'Management experience'],
                'responsibilities' => ['Strategic planning', 'Operations management', 'Budget oversight'],
                'is_active' => true,
            ],
            [
                'title' => 'Administrative Assistant',
                'description' => 'Provides administrative support',
                'department_id' => $departments['ADMIN']->id,
                'level' => 'Entry',
                'min_salary' => 4000000,
                'max_salary' => 7000000,
                'requirements' => ['High school diploma', 'Computer skills'],
                'responsibilities' => ['Data entry', 'Filing', 'Customer service'],
                'is_active' => true,
            ],

            // Human Resources
            [
                'title' => 'HR Manager',
                'description' => 'Manages human resources operations',
                'department_id' => $departments['HR']->id,
                'level' => 'Senior',
                'min_salary' => 18000000,
                'max_salary' => 30000000,
                'requirements' => ['HR degree', 'Management experience', 'HR certification'],
                'responsibilities' => ['Employee relations', 'Recruitment', 'Policy development'],
                'is_active' => true,
            ],
            [
                'title' => 'HR Specialist',
                'description' => 'Handles specific HR functions',
                'department_id' => $departments['HR']->id,
                'level' => 'Mid',
                'min_salary' => 8000000,
                'max_salary' => 15000000,
                'requirements' => ['HR degree', 'HR experience'],
                'responsibilities' => ['Recruitment', 'Benefits administration', 'Training coordination'],
                'is_active' => true,
            ],

            // IT Department
            [
                'title' => 'IT Manager',
                'description' => 'Manages IT infrastructure and systems',
                'department_id' => $departments['IT']->id,
                'level' => 'Senior',
                'min_salary' => 20000000,
                'max_salary' => 35000000,
                'requirements' => ['IT degree', 'Management experience', 'Technical certifications'],
                'responsibilities' => ['System management', 'Team leadership', 'Strategic planning'],
                'is_active' => true,
            ],
            [
                'title' => 'System Administrator',
                'description' => 'Maintains computer systems and networks',
                'department_id' => $departments['IT']->id,
                'level' => 'Mid',
                'min_salary' => 10000000,
                'max_salary' => 18000000,
                'requirements' => ['IT degree', 'System administration experience'],
                'responsibilities' => ['Server maintenance', 'Network management', 'User support'],
                'is_active' => true,
            ],

            // Laboratory
            [
                'title' => 'Laboratory Technologist',
                'description' => 'Performs laboratory tests and analysis',
                'department_id' => $departments['LAB']->id,
                'level' => 'Mid',
                'min_salary' => 8000000,
                'max_salary' => 15000000,
                'requirements' => ['Medical technology degree', 'Laboratory certification'],
                'responsibilities' => ['Sample analysis', 'Quality control', 'Equipment maintenance'],
                'is_active' => true,
            ],

            // Pharmacy
            [
                'title' => 'Pharmacist',
                'description' => 'Dispenses medications and provides pharmaceutical care',
                'department_id' => $departments['PHARM']->id,
                'level' => 'Senior',
                'min_salary' => 15000000,
                'max_salary' => 25000000,
                'requirements' => ['Pharmacy degree', 'Pharmacist license'],
                'responsibilities' => ['Medication dispensing', 'Drug interaction checking', 'Patient counseling'],
                'is_active' => true,
            ],

            // Maintenance
            [
                'title' => 'Maintenance Supervisor',
                'description' => 'Supervises facility maintenance operations',
                'department_id' => $departments['MAINT']->id,
                'level' => 'Mid',
                'min_salary' => 10000000,
                'max_salary' => 18000000,
                'requirements' => ['Technical education', 'Maintenance experience'],
                'responsibilities' => ['Facility maintenance', 'Team supervision', 'Work scheduling'],
                'is_active' => true,
            ],
        ];

        foreach ($positions as $position) {
            Position::create($position);
        }

        $this->command->info('Positions seeded successfully!');
    }
}
