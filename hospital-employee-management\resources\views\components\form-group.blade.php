@props(['label', 'name', 'required' => false, 'error' => null])

<div {{ $attributes->merge(['class' => 'mb-4']) }}>
    @if($label)
        <x-input-label for="{{ $name }}" :value="$label" class="{{ $required ? 'required' : '' }}" />
    @endif
    
    <div class="mt-1">
        {{ $slot }}
    </div>
    
    @if($error)
        <x-input-error :messages="$error" class="mt-2" />
    @endif
</div>

<style>
.required::after {
    content: " *";
    color: #ef4444;
}
</style>
