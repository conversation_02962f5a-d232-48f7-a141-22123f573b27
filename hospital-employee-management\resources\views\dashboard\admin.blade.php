<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Admin Dashboard') }}
            </h2>
            <div class="text-sm text-gray-600">
                {{ now()->format('l, F j, Y') }}
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <x-stats-card 
                    title="Total Employees" 
                    :value="$stats['total_employees']"
                    color="blue"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path d=\'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Active Employees" 
                    :value="$stats['active_employees']"
                    color="green"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path d=\'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Departments" 
                    :value="$stats['total_departments']"
                    color="purple"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path d=\'M19 7l-.867 12.142A2 2 0 0116.138 21H3.862a2 2 0 01-1.995-1.858L1 7m18 0l-2-4H5L3 7m16 0H3m0 0l.5 2.5M19 7l-.5 2.5\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Pending Leaves" 
                    :value="$stats['pending_leaves']"
                    color="yellow"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\' clip-rule=\'evenodd\'></path></svg>'"
                />
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- Recent Employees -->
                <x-card title="Recent Employees" class="lg:col-span-1">
                    <div class="space-y-3">
                        @forelse($recentEmployees as $employee)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $employee->full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $employee->department->name ?? 'N/A' }}</p>
                                    <p class="text-xs text-gray-500">{{ $employee->position->title ?? 'N/A' }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-600">{{ $employee->hire_date->format('M j') }}</p>
                                    <x-badge type="success" size="xs">New</x-badge>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No recent employees</p>
                        @endforelse
                    </div>
                    
                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('employees.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            View all employees →
                        </a>
                    </div>
                </x-card>

                <!-- Pending Leave Requests -->
                <x-card title="Pending Leave Requests" class="lg:col-span-1">
                    <div class="space-y-3">
                        @forelse($pendingLeaves as $leave)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $leave->employee->full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $leave->leaveType->name }}</p>
                                    <p class="text-xs text-gray-500">{{ $leave->start_date->format('M j') }} - {{ $leave->end_date->format('M j') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-600">{{ $leave->days_requested }} days</p>
                                    <x-badge type="warning" size="xs">Pending</x-badge>
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No pending requests</p>
                        @endforelse
                    </div>
                    
                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('leaves.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            View all leaves →
                        </a>
                    </div>
                </x-card>

                <!-- Today's Attendance -->
                <x-card title="Today's Attendance" class="lg:col-span-1">
                    <div class="space-y-3">
                        @forelse($todayAttendance as $attendance)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $attendance->employee->full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $attendance->employee->department->name ?? 'N/A' }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-600">
                                        {{ $attendance->clock_in ? $attendance->clock_in->format('H:i') : 'Not clocked in' }}
                                    </p>
                                    @if($attendance->clock_in && !$attendance->clock_out)
                                        <x-badge type="success" size="xs">Present</x-badge>
                                    @elseif($attendance->clock_out)
                                        <x-badge type="info" size="xs">Completed</x-badge>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-4">No attendance records</p>
                        @endforelse
                    </div>
                    
                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('attendance.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            View all attendance →
                        </a>
                    </div>
                </x-card>
            </div>

            <!-- Department Statistics -->
            <x-card title="Department Overview">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($departmentStats as $dept)
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-900">{{ $dept['name'] }}</h4>
                            <div class="mt-2 flex justify-between text-sm">
                                <span class="text-gray-600">Total: {{ $dept['total'] }}</span>
                                <span class="text-green-600">Active: {{ $dept['active'] }}</span>
                            </div>
                            <div class="mt-2 bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ $dept['total'] > 0 ? ($dept['active'] / $dept['total']) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </x-card>

            <!-- Quick Actions -->
            <x-card title="Quick Actions">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    @can('create employees')
                        <a href="{{ route('employees.create') }}" class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            <svg class="w-8 h-8 text-blue-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="text-sm font-medium text-blue-900">Add Employee</span>
                        </a>
                    @endcan
                    
                    @can('create departments')
                        <a href="{{ route('departments.create') }}" class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                            <svg class="w-8 h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <span class="text-sm font-medium text-green-900">Add Department</span>
                        </a>
                    @endcan
                    
                    @can('create positions')
                        <a href="{{ route('positions.create') }}" class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                            <svg class="w-8 h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                            </svg>
                            <span class="text-sm font-medium text-purple-900">Add Position</span>
                        </a>
                    @endcan
                    
                    <a href="{{ route('attendance.index') }}" class="flex flex-col items-center p-4 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors">
                        <svg class="w-8 h-8 text-yellow-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm font-medium text-yellow-900">View Attendance</span>
                    </a>
                </div>
            </x-card>
        </div>
    </div>
</x-app-layout>
