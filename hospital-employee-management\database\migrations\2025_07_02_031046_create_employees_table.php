<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->string('employee_id', 20)->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('department_id')->constrained()->onDelete('restrict');
            $table->foreignId('position_id')->constrained()->onDelete('restrict');
            $table->foreignId('supervisor_id')->nullable()->constrained('employees')->onDelete('set null');

            // Personal Information
            $table->string('first_name');
            $table->string('middle_name')->nullable();
            $table->string('last_name');
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female', 'other']);
            $table->enum('marital_status', ['single', 'married', 'divorced', 'widowed']);
            $table->string('nationality')->default('Indonesian');
            $table->string('id_number', 20)->unique(); // National ID

            // Contact Information
            $table->string('phone', 20);
            $table->string('personal_email')->nullable();
            $table->text('address');
            $table->string('city');
            $table->string('postal_code', 10);

            // Employment Information
            $table->date('hire_date');
            $table->date('probation_end_date')->nullable();
            $table->enum('employment_type', ['full_time', 'part_time', 'contract', 'intern']);
            $table->enum('employment_status', ['active', 'inactive', 'terminated', 'resigned']);
            $table->decimal('current_salary', 10, 2);
            $table->string('work_location')->nullable();

            // Medical Information (for hospital staff)
            $table->string('medical_license_number')->nullable();
            $table->date('medical_license_expiry')->nullable();
            $table->json('certifications')->nullable(); // Store medical certifications
            $table->json('specializations')->nullable(); // Medical specializations

            // Additional Information
            $table->string('bank_account_number')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('tax_id')->nullable();
            $table->string('profile_photo')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['employee_id']);
            $table->index(['department_id', 'employment_status']);
            $table->index(['position_id', 'employment_status']);
            $table->index(['employment_status']);
            $table->index(['hire_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
