<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePositionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create positions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'department_id' => ['required', 'exists:departments,id'],
            'level' => ['required', 'string', 'max:50'],
            'min_salary' => ['nullable', 'numeric', 'min:0'],
            'max_salary' => ['nullable', 'numeric', 'min:0', 'gte:min_salary'],
            'requirements' => ['nullable', 'array'],
            'requirements.*' => ['string', 'max:255'],
            'responsibilities' => ['nullable', 'array'],
            'responsibilities.*' => ['string', 'max:255'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Position title is required.',
            'department_id.required' => 'Please select a department.',
            'department_id.exists' => 'The selected department is invalid.',
            'level.required' => 'Position level is required.',
            'min_salary.numeric' => 'Minimum salary must be a number.',
            'min_salary.min' => 'Minimum salary cannot be negative.',
            'max_salary.numeric' => 'Maximum salary must be a number.',
            'max_salary.min' => 'Maximum salary cannot be negative.',
            'max_salary.gte' => 'Maximum salary must be greater than or equal to minimum salary.',
            'requirements.*.string' => 'Each requirement must be text.',
            'requirements.*.max' => 'Each requirement cannot exceed 255 characters.',
            'responsibilities.*.string' => 'Each responsibility must be text.',
            'responsibilities.*.max' => 'Each responsibility cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'department_id' => 'department',
            'min_salary' => 'minimum salary',
            'max_salary' => 'maximum salary',
            'is_active' => 'active status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string arrays to proper arrays if needed
        if ($this->has('requirements') && is_string($this->requirements)) {
            $this->merge([
                'requirements' => array_filter(array_map('trim', explode(',', $this->requirements)))
            ]);
        }

        if ($this->has('responsibilities') && is_string($this->responsibilities)) {
            $this->merge([
                'responsibilities' => array_filter(array_map('trim', explode(',', $this->responsibilities)))
            ]);
        }

        // Set default active status
        if (!$this->has('is_active')) {
            $this->merge([
                'is_active' => true
            ]);
        }

        // Convert salary values to proper numeric format
        if ($this->has('min_salary') && is_string($this->min_salary)) {
            $this->merge([
                'min_salary' => (float) str_replace(',', '', $this->min_salary)
            ]);
        }

        if ($this->has('max_salary') && is_string($this->max_salary)) {
            $this->merge([
                'max_salary' => (float) str_replace(',', '', $this->max_salary)
            ]);
        }
    }
}
