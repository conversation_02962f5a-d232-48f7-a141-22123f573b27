@props(['type' => 'default', 'size' => 'sm'])

@php
$classes = [
    'default' => 'bg-gray-100 text-gray-800',
    'success' => 'bg-green-100 text-green-800',
    'warning' => 'bg-yellow-100 text-yellow-800',
    'danger' => 'bg-red-100 text-red-800',
    'info' => 'bg-blue-100 text-blue-800',
    'primary' => 'bg-indigo-100 text-indigo-800',
];

$sizeClasses = [
    'xs' => 'px-2 py-1 text-xs',
    'sm' => 'px-2.5 py-0.5 text-xs',
    'md' => 'px-3 py-1 text-sm',
    'lg' => 'px-4 py-2 text-base',
];

$class = ($classes[$type] ?? $classes['default']) . ' ' . ($sizeClasses[$size] ?? $sizeClasses['sm']);
@endphp

<span {{ $attributes->merge(['class' => "inline-flex items-center font-medium rounded-full {$class}"]) }}>
    {{ $slot }}
</span>
