<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Department Dashboard') }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">{{ $department->name }}</p>
            </div>
            <div class="text-sm text-gray-600">
                {{ now()->format('l, F j, Y') }}
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Department Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <x-stats-card 
                    title="Total Employees" 
                    :value="$stats['department_employees']"
                    color="blue"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path d=\'M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Active" 
                    :value="$stats['active_employees']"
                    color="green"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\' clip-rule=\'evenodd\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="On Leave" 
                    :value="$stats['employees_on_leave']"
                    color="yellow"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\' clip-rule=\'evenodd\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Pending Requests" 
                    :value="$stats['pending_leaves']"
                    color="red"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\' clip-rule=\'evenodd\'></path></svg>'"
                />
                
                <x-stats-card 
                    title="Present Today" 
                    :value="$stats['present_today']"
                    color="indigo"
                    :icon="'<svg class=\'w-5 h-5 text-white\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\' clip-rule=\'evenodd\'></path></svg>'"
                />
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- Pending Leave Requests -->
                <x-card title="Pending Leave Requests" subtitle="Requests requiring your approval">
                    <div class="space-y-3">
                        @forelse($pendingLeaves as $leave)
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900">{{ $leave->employee->full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $leave->leaveType->name }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ $leave->start_date->format('M j, Y') }} - {{ $leave->end_date->format('M j, Y') }}
                                        ({{ $leave->days_requested }} days)
                                    </p>
                                    @if($leave->reason)
                                        <p class="text-xs text-gray-500 mt-1">{{ Str::limit($leave->reason, 50) }}</p>
                                    @endif
                                </div>
                                <div class="flex space-x-2">
                                    @can('approve leaves')
                                        <form method="POST" action="{{ route('leaves.approve', $leave) }}" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="text-green-600 hover:text-green-900 text-sm font-medium">
                                                Approve
                                            </button>
                                        </form>
                                        <form method="POST" action="{{ route('leaves.reject', $leave) }}" class="inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                Reject
                                            </button>
                                        </form>
                                    @endcan
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-8">No pending leave requests</p>
                        @endforelse
                    </div>
                    
                    @if($pendingLeaves->count() > 0)
                        <div class="mt-4 pt-4 border-t">
                            <a href="{{ route('leaves.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                View all leave requests →
                            </a>
                        </div>
                    @endif
                </x-card>

                <!-- Today's Attendance -->
                <x-card title="Today's Attendance" subtitle="Department attendance overview">
                    <div class="space-y-3">
                        @forelse($todayAttendance as $attendance)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $attendance->employee->full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ $attendance->employee->position->title ?? 'N/A' }}</p>
                                </div>
                                <div class="text-right">
                                    @if($attendance->clock_in)
                                        <p class="text-sm text-gray-600">In: {{ $attendance->clock_in->format('H:i') }}</p>
                                        @if($attendance->clock_out)
                                            <p class="text-sm text-gray-600">Out: {{ $attendance->clock_out->format('H:i') }}</p>
                                            <x-badge type="info" size="xs">Completed</x-badge>
                                        @else
                                            <x-badge type="success" size="xs">Present</x-badge>
                                        @endif
                                    @else
                                        <x-badge type="warning" size="xs">Not clocked in</x-badge>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 text-center py-8">No attendance records for today</p>
                        @endforelse
                    </div>
                    
                    <div class="mt-4 pt-4 border-t">
                        <a href="{{ route('attendance.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                            View all attendance →
                        </a>
                    </div>
                </x-card>
            </div>

            <!-- Department Employees -->
            <x-card title="Department Employees" subtitle="All employees in your department">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($departmentEmployees as $employee)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $employee->full_name }}</p>
                                            <p class="text-sm text-gray-500">{{ $employee->employee_id }}</p>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $employee->position->title ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $employee->user->email }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <x-badge :type="$employee->employment_status === 'active' ? 'success' : 'warning'">
                                            {{ ucfirst($employee->employment_status) }}
                                        </x-badge>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="{{ route('employees.show', $employee) }}" class="text-indigo-600 hover:text-indigo-900">
                                            View
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4 pt-4 border-t">
                    <a href="{{ route('employees.index') }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                        View all employees →
                    </a>
                </div>
            </x-card>

            <!-- Quick Actions -->
            <x-card title="Quick Actions">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    @can('request leave')
                        <a href="{{ route('leaves.create') }}" class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            <svg class="w-8 h-8 text-blue-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-sm font-medium text-blue-900">Request Leave</span>
                        </a>
                    @endcan
                    
                    <a href="{{ route('attendance.clock') }}" class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                        <svg class="w-8 h-8 text-green-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm font-medium text-green-900">Clock In/Out</span>
                    </a>
                    
                    <a href="{{ route('employees.profile') }}" class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                        <svg class="w-8 h-8 text-purple-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span class="text-sm font-medium text-purple-900">My Profile</span>
                    </a>
                    
                    <a href="{{ route('leaves.index') }}" class="flex flex-col items-center p-4 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors">
                        <svg class="w-8 h-8 text-yellow-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <span class="text-sm font-medium text-yellow-900">Leave Requests</span>
                    </a>
                </div>
            </x-card>
        </div>
    </div>
</x-app-layout>
