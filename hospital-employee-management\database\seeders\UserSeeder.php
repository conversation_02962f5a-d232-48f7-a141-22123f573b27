<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Position;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get departments and positions for reference
        $departments = Department::all()->keyBy('code');
        $positions = Position::all()->keyBy('title');

        // Create Super Admin
        $superAdmin = User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        $superAdmin->assignRole('Super Admin');

        // Create Super Admin Employee Profile
        Employee::create([
            'employee_id' => 'EMP001',
            'user_id' => $superAdmin->id,
            'department_id' => $departments['ADMIN']->id,
            'position_id' => $positions['Hospital Administrator']->id,
            'first_name' => 'Super',
            'last_name' => 'Administrator',
            'date_of_birth' => Carbon::parse('1980-01-01'),
            'gender' => 'male',
            'marital_status' => 'married',
            'nationality' => 'Indonesian',
            'id_number' => '3201010180001',
            'phone' => '+62-812-3456-7890',
            'personal_email' => '<EMAIL>',
            'address' => 'Jl. Admin No. 1, Jakarta',
            'city' => 'Jakarta',
            'postal_code' => '12345',
            'hire_date' => Carbon::parse('2020-01-01'),
            'employment_type' => 'full_time',
            'employment_status' => 'active',
            'current_salary' => 50000000,
            'work_location' => 'Main Building',
        ]);

        // Create HR Manager
        $hrManager = User::create([
            'name' => 'HR Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        $hrManager->assignRole('HR Manager');

        Employee::create([
            'employee_id' => 'EMP002',
            'user_id' => $hrManager->id,
            'department_id' => $departments['HR']->id,
            'position_id' => $positions['HR Manager']->id,
            'first_name' => 'Sarah',
            'last_name' => 'Johnson',
            'date_of_birth' => Carbon::parse('1985-03-15'),
            'gender' => 'female',
            'marital_status' => 'single',
            'nationality' => 'Indonesian',
            'id_number' => '3201031585002',
            'phone' => '+62-813-4567-8901',
            'personal_email' => '<EMAIL>',
            'address' => 'Jl. HR No. 2, Jakarta',
            'city' => 'Jakarta',
            'postal_code' => '12346',
            'hire_date' => Carbon::parse('2021-02-01'),
            'employment_type' => 'full_time',
            'employment_status' => 'active',
            'current_salary' => 25000000,
            'work_location' => 'Main Building',
        ]);

        // Create Department Head (Emergency)
        $deptHead = User::create([
            'name' => 'Dr. Emergency Head',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        $deptHead->assignRole('Department Head');

        $emergencyHead = Employee::create([
            'employee_id' => 'EMP003',
            'user_id' => $deptHead->id,
            'department_id' => $departments['ED']->id,
            'position_id' => $positions['Emergency Medicine Physician']->id,
            'first_name' => 'Michael',
            'last_name' => 'Emergency',
            'date_of_birth' => Carbon::parse('1978-07-20'),
            'gender' => 'male',
            'marital_status' => 'married',
            'nationality' => 'Indonesian',
            'id_number' => '3201072078003',
            'phone' => '+62-814-5678-9012',
            'personal_email' => '<EMAIL>',
            'address' => 'Jl. Emergency No. 3, Jakarta',
            'city' => 'Jakarta',
            'postal_code' => '12347',
            'hire_date' => Carbon::parse('2019-03-01'),
            'employment_type' => 'full_time',
            'employment_status' => 'active',
            'current_salary' => 35000000,
            'work_location' => 'Emergency Department',
            'medical_license_number' => 'ML001234',
            'medical_license_expiry' => Carbon::parse('2025-12-31'),
            'specializations' => ['Emergency Medicine', 'Trauma Care'],
        ]);

        // Update Emergency Department head
        $departments['ED']->update(['head_id' => $emergencyHead->id]);

        // Create Regular Employee (Nurse)
        $employee = User::create([
            'name' => 'Nurse Employee',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        $employee->assignRole('Employee');

        Employee::create([
            'employee_id' => 'EMP004',
            'user_id' => $employee->id,
            'department_id' => $departments['ED']->id,
            'position_id' => $positions['Emergency Nurse']->id,
            'supervisor_id' => $emergencyHead->id,
            'first_name' => 'Lisa',
            'last_name' => 'Nurse',
            'date_of_birth' => Carbon::parse('1990-05-10'),
            'gender' => 'female',
            'marital_status' => 'single',
            'nationality' => 'Indonesian',
            'id_number' => '3201051090004',
            'phone' => '+62-815-6789-0123',
            'personal_email' => '<EMAIL>',
            'address' => 'Jl. Nurse No. 4, Jakarta',
            'city' => 'Jakarta',
            'postal_code' => '12348',
            'hire_date' => Carbon::parse('2022-01-15'),
            'employment_type' => 'full_time',
            'employment_status' => 'active',
            'current_salary' => 12000000,
            'work_location' => 'Emergency Department',
            'certifications' => ['BLS', 'ACLS', 'Emergency Nursing'],
        ]);

        // Create IT Manager
        $itManager = User::create([
            'name' => 'IT Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        $itManager->assignRole('Department Head');

        $itHead = Employee::create([
            'employee_id' => 'EMP005',
            'user_id' => $itManager->id,
            'department_id' => $departments['IT']->id,
            'position_id' => $positions['IT Manager']->id,
            'first_name' => 'John',
            'last_name' => 'TechLead',
            'date_of_birth' => Carbon::parse('1982-11-25'),
            'gender' => 'male',
            'marital_status' => 'married',
            'nationality' => 'Indonesian',
            'id_number' => '3201112582005',
            'phone' => '+62-816-7890-1234',
            'personal_email' => '<EMAIL>',
            'address' => 'Jl. IT No. 5, Jakarta',
            'city' => 'Jakarta',
            'postal_code' => '12349',
            'hire_date' => Carbon::parse('2020-06-01'),
            'employment_type' => 'full_time',
            'employment_status' => 'active',
            'current_salary' => 28000000,
            'work_location' => 'IT Department',
        ]);

        // Update IT Department head
        $departments['IT']->update(['head_id' => $itHead->id]);

        // Create a few more sample employees
        $sampleEmployees = [
            [
                'name' => 'Dr. Internal Medicine',
                'email' => '<EMAIL>',
                'department' => 'IM',
                'position' => 'Internal Medicine Physician',
                'role' => 'Employee',
                'employee_data' => [
                    'employee_id' => 'EMP006',
                    'first_name' => 'David',
                    'last_name' => 'Internal',
                    'date_of_birth' => '1983-09-12',
                    'gender' => 'male',
                    'current_salary' => 30000000,
                    'medical_license_number' => 'ML001235',
                    'medical_license_expiry' => '2025-12-31',
                    'specializations' => ['Internal Medicine', 'Cardiology'],
                ]
            ],
            [
                'name' => 'Pharmacist Lead',
                'email' => '<EMAIL>',
                'department' => 'PHARM',
                'position' => 'Pharmacist',
                'role' => 'Employee',
                'employee_data' => [
                    'employee_id' => 'EMP007',
                    'first_name' => 'Maria',
                    'last_name' => 'Pharmacy',
                    'date_of_birth' => '1987-04-18',
                    'gender' => 'female',
                    'current_salary' => 20000000,
                ]
            ],
        ];

        foreach ($sampleEmployees as $index => $empData) {
            $user = User::create([
                'name' => $empData['name'],
                'email' => $empData['email'],
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]);
            $user->assignRole($empData['role']);

            $employeeData = array_merge([
                'user_id' => $user->id,
                'department_id' => $departments[$empData['department']]->id,
                'position_id' => $positions[$empData['position']]->id,
                'nationality' => 'Indonesian',
                'id_number' => '320101018000' . (6 + $index),
                'phone' => '+62-817-890' . (1234 + $index),
                'personal_email' => strtolower(str_replace(' ', '.', $empData['name'])) . '@email.com',
                'address' => 'Jl. Sample No. ' . (6 + $index) . ', Jakarta',
                'city' => 'Jakarta',
                'postal_code' => '1234' . (6 + $index),
                'hire_date' => Carbon::parse('2021-01-01'),
                'employment_type' => 'full_time',
                'employment_status' => 'active',
                'work_location' => 'Main Building',
                'marital_status' => 'single',
            ], $empData['employee_data']);

            Employee::create($employeeData);
        }

        $this->command->info('Users and employees seeded successfully!');
    }
}
