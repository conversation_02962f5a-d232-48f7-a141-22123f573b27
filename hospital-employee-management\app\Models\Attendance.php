<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Attendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'schedule_id',
        'date',
        'clock_in',
        'clock_out',
        'break_start',
        'break_end',
        'total_hours',
        'overtime_hours',
        'late_minutes',
        'early_departure_minutes',
        'status',
        'notes',
        'clock_in_location',
        'clock_out_location',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'date' => 'date',
        'clock_in' => 'datetime',
        'clock_out' => 'datetime',
        'break_start' => 'datetime',
        'break_end' => 'datetime',
        'total_hours' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'late_minutes' => 'integer',
        'early_departure_minutes' => 'integer',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the employee this attendance record belongs to
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the schedule this attendance is based on
     */
    public function schedule(): BelongsTo
    {
        return $this->belongsTo(Schedule::class);
    }

    /**
     * Get the approver of this attendance record
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'approved_by');
    }

    /**
     * Scope to get attendance for a specific date
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * Scope to get attendance for a date range
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope to get attendance by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending approval attendance
     */
    public function scopePendingApproval($query)
    {
        return $query->where('status', 'pending_approval');
    }

    /**
     * Scope to get approved attendance
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Calculate total working hours
     */
    public function calculateTotalHours(): float
    {
        if (!$this->clock_in || !$this->clock_out) {
            return 0;
        }

        $clockIn = Carbon::parse($this->clock_in);
        $clockOut = Carbon::parse($this->clock_out);
        
        // Handle overnight shifts
        if ($clockOut->lt($clockIn)) {
            $clockOut->addDay();
        }
        
        $totalMinutes = $clockIn->diffInMinutes($clockOut);
        
        // Subtract break time if available
        if ($this->break_start && $this->break_end) {
            $breakStart = Carbon::parse($this->break_start);
            $breakEnd = Carbon::parse($this->break_end);
            $breakMinutes = $breakStart->diffInMinutes($breakEnd);
            $totalMinutes -= $breakMinutes;
        }
        
        return round($totalMinutes / 60, 2);
    }

    /**
     * Calculate overtime hours based on schedule
     */
    public function calculateOvertimeHours(): float
    {
        if (!$this->schedule) {
            return 0;
        }
        
        $scheduledHours = $this->schedule->working_hours;
        $actualHours = $this->total_hours ?: $this->calculateTotalHours();
        
        return max(0, $actualHours - $scheduledHours);
    }

    /**
     * Calculate late minutes
     */
    public function calculateLateMinutes(): int
    {
        if (!$this->clock_in || !$this->schedule) {
            return 0;
        }
        
        $scheduledStart = Carbon::parse($this->schedule->start_time);
        $actualStart = Carbon::parse($this->clock_in);
        
        // Set the same date for comparison
        $scheduledStart->setDate($actualStart->year, $actualStart->month, $actualStart->day);
        
        return max(0, $actualStart->diffInMinutes($scheduledStart));
    }

    /**
     * Calculate early departure minutes
     */
    public function calculateEarlyDepartureMinutes(): int
    {
        if (!$this->clock_out || !$this->schedule) {
            return 0;
        }
        
        $scheduledEnd = Carbon::parse($this->schedule->end_time);
        $actualEnd = Carbon::parse($this->clock_out);
        
        // Set the same date for comparison
        $scheduledEnd->setDate($actualEnd->year, $actualEnd->month, $actualEnd->day);
        
        // Handle overnight shifts
        if ($scheduledEnd->lt(Carbon::parse($this->schedule->start_time))) {
            $scheduledEnd->addDay();
        }
        
        return max(0, $scheduledEnd->diffInMinutes($actualEnd));
    }

    /**
     * Check if employee is currently clocked in
     */
    public function isClockedIn(): bool
    {
        return $this->clock_in && !$this->clock_out;
    }

    /**
     * Check if employee is on break
     */
    public function isOnBreak(): bool
    {
        return $this->break_start && !$this->break_end;
    }

    /**
     * Auto-calculate attendance metrics
     */
    public function updateCalculatedFields(): void
    {
        $this->total_hours = $this->calculateTotalHours();
        $this->overtime_hours = $this->calculateOvertimeHours();
        $this->late_minutes = $this->calculateLateMinutes();
        $this->early_departure_minutes = $this->calculateEarlyDepartureMinutes();
    }
}
