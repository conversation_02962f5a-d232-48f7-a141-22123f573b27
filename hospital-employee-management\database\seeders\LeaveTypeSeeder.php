<?php

namespace Database\Seeders;

use App\Models\LeaveType;
use Illuminate\Database\Seeder;

class LeaveTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $leaveTypes = [
            [
                'name' => 'Annual Leave',
                'description' => 'Yearly vacation leave for rest and recreation',
                'max_days_per_year' => 12,
                'requires_approval' => true,
                'requires_document' => false,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#28a745', // Green
            ],
            [
                'name' => 'Sick Leave',
                'description' => 'Leave for illness or medical treatment',
                'max_days_per_year' => 30,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#dc3545', // Red
            ],
            [
                'name' => 'Maternity Leave',
                'description' => 'Leave for childbirth and post-natal care',
                'max_days_per_year' => 90,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#e83e8c', // Pink
            ],
            [
                'name' => 'Paternity Leave',
                'description' => 'Leave for fathers after childbirth',
                'max_days_per_year' => 7,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#17a2b8', // Cyan
            ],
            [
                'name' => 'Emergency Leave',
                'description' => 'Leave for family emergencies or urgent personal matters',
                'max_days_per_year' => 5,
                'requires_approval' => true,
                'requires_document' => false,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#fd7e14', // Orange
            ],
            [
                'name' => 'Bereavement Leave',
                'description' => 'Leave for death of immediate family member',
                'max_days_per_year' => 3,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#6c757d', // Gray
            ],
            [
                'name' => 'Marriage Leave',
                'description' => 'Leave for employee\'s wedding',
                'max_days_per_year' => 3,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#ffc107', // Yellow
            ],
            [
                'name' => 'Study Leave',
                'description' => 'Leave for educational purposes or training',
                'max_days_per_year' => 10,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => false,
                'is_active' => true,
                'color' => '#6f42c1', // Purple
            ],
            [
                'name' => 'Unpaid Leave',
                'description' => 'Leave without pay for personal reasons',
                'max_days_per_year' => 30,
                'requires_approval' => true,
                'requires_document' => false,
                'is_paid' => false,
                'is_active' => true,
                'color' => '#495057', // Dark gray
            ],
            [
                'name' => 'Compensatory Leave',
                'description' => 'Time off in lieu of overtime work',
                'max_days_per_year' => 15,
                'requires_approval' => true,
                'requires_document' => false,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#20c997', // Teal
            ],
            [
                'name' => 'Medical Leave',
                'description' => 'Extended leave for serious medical conditions',
                'max_days_per_year' => 60,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => false,
                'is_active' => true,
                'color' => '#dc3545', // Red
            ],
            [
                'name' => 'Pilgrimage Leave',
                'description' => 'Leave for religious pilgrimage (Hajj/Umrah)',
                'max_days_per_year' => 40,
                'requires_approval' => true,
                'requires_document' => true,
                'is_paid' => true,
                'is_active' => true,
                'color' => '#007bff', // Blue
            ],
        ];

        foreach ($leaveTypes as $leaveType) {
            LeaveType::create($leaveType);
        }

        $this->command->info('Leave types seeded successfully!');
    }
}
