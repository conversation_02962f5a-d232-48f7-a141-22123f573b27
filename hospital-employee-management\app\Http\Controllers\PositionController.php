<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePositionRequest;
use App\Models\Position;
use App\Models\Department;
use Illuminate\Http\Request;

class PositionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view positions');

        $query = Position::with(['department', 'employees' => function ($q) {
            $q->where('employment_status', 'active');
        }])
        ->withCount(['employees', 'activeEmployees'])
        ->when($request->search, function ($q) use ($request) {
            $q->where('title', 'like', "%{$request->search}%")
              ->orWhereHas('department', function ($query) use ($request) {
                  $query->where('name', 'like', "%{$request->search}%");
              });
        })
        ->when($request->department, function ($q) use ($request) {
            $q->where('department_id', $request->department);
        })
        ->when($request->level, function ($q) use ($request) {
            $q->where('level', $request->level);
        })
        ->when($request->has('status'), function ($q) use ($request) {
            $q->where('is_active', $request->status === 'active');
        });

        $positions = $query->latest()->paginate(15);
        $departments = Department::active()->get();
        $levels = Position::distinct()->pluck('level')->filter()->sort();

        return view('positions.index', compact('positions', 'departments', 'levels'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create positions');

        $departments = Department::active()->get();

        return view('positions.create', compact('departments'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePositionRequest $request)
    {
        $position = Position::create($request->validated());

        return redirect()->route('positions.show', $position)
                        ->with('success', 'Position created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Position $position)
    {
        $this->authorize('view positions');

        $position->load([
            'department',
            'employees.user',
            'activeEmployees'
        ]);

        // Get position statistics
        $stats = [
            'total_employees' => $position->employees()->count(),
            'active_employees' => $position->activeEmployees()->count(),
            'average_salary' => $position->activeEmployees()->avg('current_salary'),
            'min_current_salary' => $position->activeEmployees()->min('current_salary'),
            'max_current_salary' => $position->activeEmployees()->max('current_salary'),
        ];

        return view('positions.show', compact('position', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Position $position)
    {
        $this->authorize('edit positions');

        $departments = Department::active()->get();

        return view('positions.edit', compact('position', 'departments'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StorePositionRequest $request, Position $position)
    {
        $position->update($request->validated());

        return redirect()->route('positions.show', $position)
                        ->with('success', 'Position updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Position $position)
    {
        $this->authorize('delete positions');

        // Check if position can be deleted
        if (!$position->canBeDeleted()) {
            return back()->with('error', 'Cannot delete position with active employees.');
        }

        $position->delete();

        return redirect()->route('positions.index')
                        ->with('success', 'Position deleted successfully.');
    }

    /**
     * Toggle position status
     */
    public function toggleStatus(Position $position)
    {
        $this->authorize('edit positions');

        $position->update(['is_active' => !$position->is_active]);

        $status = $position->is_active ? 'activated' : 'deactivated';
        
        return back()->with('success', "Position {$status} successfully.");
    }

    /**
     * Get positions by department (AJAX)
     */
    public function byDepartment(Department $department)
    {
        $this->authorize('view positions');

        $positions = $department->positions()
            ->active()
            ->select('id', 'title', 'level', 'min_salary', 'max_salary')
            ->get();

        return response()->json($positions);
    }

    /**
     * Get position details (AJAX)
     */
    public function details(Position $position)
    {
        $this->authorize('view positions');

        $position->load('department');

        $data = [
            'id' => $position->id,
            'title' => $position->title,
            'description' => $position->description,
            'department' => $position->department->name,
            'level' => $position->level,
            'salary_range' => $position->salary_range,
            'requirements' => $position->requirements,
            'responsibilities' => $position->responsibilities,
            'employee_count' => $position->active_employee_count,
        ];

        return response()->json($data);
    }
}
