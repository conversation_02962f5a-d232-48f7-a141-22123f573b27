<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    // Test User model
    $userCount = App\Models\User::count();
    echo "✓ Users table: {$userCount} records\n";

    // Test Employee model
    $employeeCount = App\Models\Employee::count();
    echo "✓ Employees table: {$employeeCount} records\n";

    // Test Department model
    $departmentCount = App\Models\Department::count();
    echo "✓ Departments table: {$departmentCount} records\n";

    // Test Position model
    $positionCount = App\Models\Position::count();
    echo "✓ Positions table: {$positionCount} records\n";

    // Test LeaveType model
    $leaveTypeCount = App\Models\LeaveType::count();
    echo "✓ Leave Types table: {$leaveTypeCount} records\n";

    // Test Role and Permission models
    $roleCount = Spatie\Permission\Models\Role::count();
    echo "✓ Roles table: {$roleCount} records\n";

    $permissionCount = Spatie\Permission\Models\Permission::count();
    echo "✓ Permissions table: {$permissionCount} records\n";

    // Test specific field access
    $user = App\Models\User::first();
    if ($user) {
        echo "✓ User fields accessible: name={$user->name}, is_active=" . ($user->is_active ? 'true' : 'false') . "\n";
    }

    $employee = App\Models\Employee::first();
    if ($employee) {
        echo "✓ Employee fields accessible: employee_id={$employee->employee_id}, full_name={$employee->full_name}\n";
    }

    $department = App\Models\Department::first();
    if ($department) {
        echo "✓ Department fields accessible: name={$department->name}, code={$department->code}\n";
    }

    $position = App\Models\Position::first();
    if ($position) {
        echo "✓ Position fields accessible: title={$position->title}, level={$position->level}\n";
        echo "✓ Position salary range: {$position->salary_range}\n";
    }

    $leaveType = App\Models\LeaveType::first();
    if ($leaveType) {
        echo "✓ Leave Type fields accessible: name={$leaveType->name}, color={$leaveType->color}\n";
    }

    echo "\n🎉 All schema alignments verified successfully!\n";
    echo "✅ Database migrations and seeders are properly aligned.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
