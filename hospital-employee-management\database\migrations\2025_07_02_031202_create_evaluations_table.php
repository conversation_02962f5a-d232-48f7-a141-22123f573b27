<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('evaluations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->foreignId('evaluator_id')->constrained('employees')->onDelete('cascade');
            $table->date('evaluation_period_start');
            $table->date('evaluation_period_end');
            $table->string('type'); // annual, probation, performance_improvement, etc.
            $table->decimal('overall_rating', 3, 2)->nullable();
            $table->text('goals_achievements')->nullable();
            $table->text('strengths')->nullable();
            $table->text('areas_for_improvement')->nullable();
            $table->text('development_plan')->nullable();
            $table->text('evaluator_comments')->nullable();
            $table->text('employee_comments')->nullable();
            $table->enum('status', ['pending', 'completed', 'reviewed'])->default('pending');
            $table->datetime('submitted_at')->nullable();
            $table->datetime('reviewed_at')->nullable();
            $table->json('criteria_scores')->nullable();
            $table->timestamps();

            $table->index(['employee_id', 'evaluation_period_start']);
            $table->index(['evaluator_id']);
            $table->index(['status']);
            $table->index(['type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('evaluations');
    }
};
