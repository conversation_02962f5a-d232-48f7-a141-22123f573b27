<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Department;
use App\Models\Position;
use App\Models\Leave;
use App\Models\Attendance;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the dashboard
     */
    public function index(Request $request)
    {
        $this->authorize('view dashboard');

        $user = $request->user();
        $employee = $user->employee;

        // Get role-specific dashboard data
        if ($user->isSuperAdmin() || $user->isHRManager()) {
            return $this->adminDashboard();
        } elseif ($user->isDepartmentHead()) {
            return $this->departmentHeadDashboard($employee);
        } else {
            return $this->employeeDashboard($employee);
        }
    }

    /**
     * Admin/HR Manager Dashboard
     */
    private function adminDashboard()
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        // Overall statistics
        $stats = [
            'total_employees' => Employee::count(),
            'active_employees' => Employee::active()->count(),
            'total_departments' => Department::count(),
            'active_departments' => Department::active()->count(),
            'total_positions' => Position::count(),
            'pending_leaves' => Leave::pending()->count(),
            'employees_on_leave' => Leave::current()->count(),
            'new_employees_this_month' => Employee::where('hire_date', '>=', $thisMonth)->count(),
        ];

        // Recent activities
        $recentEmployees = Employee::with(['department', 'position'])
            ->latest('created_at')
            ->limit(5)
            ->get();

        $pendingLeaves = Leave::with(['employee.department', 'leaveType'])
            ->pending()
            ->latest()
            ->limit(5)
            ->get();

        $todayAttendance = Attendance::with('employee')
            ->where('date', $today)
            ->latest('clock_in')
            ->limit(10)
            ->get();

        // Department statistics
        $departmentStats = Department::withCount(['employees', 'activeEmployees'])
            ->active()
            ->get()
            ->map(function ($dept) {
                return [
                    'name' => $dept->name,
                    'total' => $dept->employees_count,
                    'active' => $dept->active_employees_count,
                ];
            });

        // Monthly trends
        $monthlyHires = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthlyHires[] = [
                'month' => $month->format('M Y'),
                'count' => Employee::whereYear('hire_date', $month->year)
                    ->whereMonth('hire_date', $month->month)
                    ->count()
            ];
        }

        return view('dashboard.admin', compact(
            'stats', 'recentEmployees', 'pendingLeaves', 'todayAttendance', 
            'departmentStats', 'monthlyHires'
        ));
    }

    /**
     * Department Head Dashboard
     */
    private function departmentHeadDashboard($employee)
    {
        $department = $employee->department;
        $today = Carbon::today();

        // Department statistics
        $stats = [
            'department_employees' => $department->employees()->count(),
            'active_employees' => $department->activeEmployees()->count(),
            'employees_on_leave' => Leave::current()
                ->whereHas('employee', function ($q) use ($department) {
                    $q->where('department_id', $department->id);
                })->count(),
            'pending_leaves' => Leave::pending()
                ->whereHas('employee', function ($q) use ($department) {
                    $q->where('department_id', $department->id);
                })->count(),
            'present_today' => Attendance::where('date', $today)
                ->whereHas('employee', function ($q) use ($department) {
                    $q->where('department_id', $department->id);
                })
                ->whereNotNull('clock_in')
                ->count(),
        ];

        // Department employees
        $departmentEmployees = $department->employees()
            ->with(['position', 'user'])
            ->active()
            ->get();

        // Pending leave requests for approval
        $pendingLeaves = Leave::with(['employee', 'leaveType'])
            ->pending()
            ->whereHas('employee', function ($q) use ($department) {
                $q->where('department_id', $department->id);
            })
            ->latest()
            ->get();

        // Today's attendance
        $todayAttendance = Attendance::with('employee')
            ->where('date', $today)
            ->whereHas('employee', function ($q) use ($department) {
                $q->where('department_id', $department->id);
            })
            ->latest('clock_in')
            ->get();

        return view('dashboard.department-head', compact(
            'stats', 'department', 'departmentEmployees', 'pendingLeaves', 'todayAttendance'
        ));
    }

    /**
     * Employee Dashboard
     */
    private function employeeDashboard($employee)
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        // Personal statistics
        $stats = [
            'years_of_service' => $employee->years_of_service,
            'leave_balance' => $this->calculateLeaveBalance($employee),
            'attendance_this_month' => Attendance::where('employee_id', $employee->id)
                ->where('date', '>=', $thisMonth)
                ->count(),
            'pending_leaves' => Leave::where('employee_id', $employee->id)
                ->pending()
                ->count(),
        ];

        // Today's attendance
        $todayAttendance = Attendance::where('employee_id', $employee->id)
            ->where('date', $today)
            ->first();

        // Today's schedule
        $todaySchedule = \App\Models\Schedule::where('employee_id', $employee->id)
            ->where('date', $today)
            ->first();

        // Recent leaves
        $recentLeaves = Leave::with('leaveType')
            ->where('employee_id', $employee->id)
            ->latest()
            ->limit(5)
            ->get();

        // Upcoming leaves
        $upcomingLeaves = Leave::with('leaveType')
            ->where('employee_id', $employee->id)
            ->upcoming()
            ->limit(3)
            ->get();

        // Recent attendance (last 7 days)
        $recentAttendance = Attendance::where('employee_id', $employee->id)
            ->where('date', '>=', $today->copy()->subDays(7))
            ->orderBy('date', 'desc')
            ->get();

        return view('dashboard.employee', compact(
            'stats', 'employee', 'todayAttendance', 'todaySchedule', 
            'recentLeaves', 'upcomingLeaves', 'recentAttendance'
        ));
    }

    /**
     * Calculate leave balance for employee
     */
    private function calculateLeaveBalance($employee)
    {
        $currentYear = Carbon::now()->year;
        $annualLeaveType = \App\Models\LeaveType::where('name', 'Annual Leave')->first();
        
        if (!$annualLeaveType) {
            return 0;
        }

        $usedDays = Leave::where('employee_id', $employee->id)
            ->where('leave_type_id', $annualLeaveType->id)
            ->where('status', 'approved')
            ->whereYear('start_date', $currentYear)
            ->sum('days_requested');

        return max(0, $annualLeaveType->max_days_per_year - $usedDays);
    }

    /**
     * Get quick stats for AJAX requests
     */
    public function quickStats()
    {
        $this->authorize('view dashboard');

        $today = Carbon::today();

        $stats = [
            'employees_present' => Attendance::where('date', $today)
                ->whereNotNull('clock_in')
                ->whereNull('clock_out')
                ->count(),
            'employees_on_break' => Attendance::where('date', $today)
                ->whereNotNull('break_start')
                ->whereNull('break_end')
                ->count(),
            'pending_leave_requests' => Leave::pending()->count(),
            'employees_on_leave' => Leave::current()->count(),
        ];

        return response()->json($stats);
    }
}
