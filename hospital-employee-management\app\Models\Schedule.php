<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Schedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'date',
        'shift_type',
        'start_time',
        'end_time',
        'break_start',
        'break_end',
        'location',
        'notes',
        'is_recurring',
        'recurring_pattern',
        'recurring_end_date',
        'status',
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'break_start' => 'datetime',
        'break_end' => 'datetime',
        'recurring_end_date' => 'date',
        'is_recurring' => 'boolean',
        'recurring_pattern' => 'array',
    ];

    /**
     * Get the employee this schedule belongs to
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Scope to get schedules for a specific date
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * Scope to get schedules for a date range
     */
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope to get active schedules
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get schedules by shift type
     */
    public function scopeByShiftType($query, $shiftType)
    {
        return $query->where('shift_type', $shiftType);
    }

    /**
     * Get the duration of the shift in hours
     */
    public function getShiftDurationAttribute(): float
    {
        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);
        
        // Handle overnight shifts
        if ($end->lt($start)) {
            $end->addDay();
        }
        
        return $start->diffInHours($end, true);
    }

    /**
     * Get the break duration in hours
     */
    public function getBreakDurationAttribute(): float
    {
        if (!$this->break_start || !$this->break_end) {
            return 0;
        }
        
        $breakStart = Carbon::parse($this->break_start);
        $breakEnd = Carbon::parse($this->break_end);
        
        return $breakStart->diffInHours($breakEnd, true);
    }

    /**
     * Get the working hours (shift duration minus break)
     */
    public function getWorkingHoursAttribute(): float
    {
        return $this->shift_duration - $this->break_duration;
    }

    /**
     * Check if this schedule conflicts with another schedule
     */
    public function conflictsWith(Schedule $otherSchedule): bool
    {
        if ($this->employee_id !== $otherSchedule->employee_id) {
            return false;
        }
        
        if (!$this->date->eq($otherSchedule->date)) {
            return false;
        }
        
        $thisStart = Carbon::parse($this->start_time);
        $thisEnd = Carbon::parse($this->end_time);
        $otherStart = Carbon::parse($otherSchedule->start_time);
        $otherEnd = Carbon::parse($otherSchedule->end_time);
        
        // Handle overnight shifts
        if ($thisEnd->lt($thisStart)) {
            $thisEnd->addDay();
        }
        if ($otherEnd->lt($otherStart)) {
            $otherEnd->addDay();
        }
        
        return $thisStart->lt($otherEnd) && $thisEnd->gt($otherStart);
    }

    /**
     * Check if the schedule is for today
     */
    public function isToday(): bool
    {
        return $this->date->isToday();
    }

    /**
     * Check if the schedule is in the past
     */
    public function isPast(): bool
    {
        return $this->date->isPast();
    }

    /**
     * Check if the schedule is in the future
     */
    public function isFuture(): bool
    {
        return $this->date->isFuture();
    }
}
