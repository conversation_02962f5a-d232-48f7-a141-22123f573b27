<?php

namespace App\Http\Requests;

use App\Models\Schedule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class StoreScheduleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('create schedules') || 
               ($this->user()->can('edit own schedule') && $this->employee_id === $this->user()->employee->id);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => ['required', 'exists:employees,id'],
            'date' => ['required', 'date', 'after_or_equal:today'],
            'shift_type' => ['required', Rule::in(['morning', 'afternoon', 'evening', 'night', 'custom'])],
            'start_time' => ['required', 'date_format:H:i'],
            'end_time' => ['required', 'date_format:H:i', 'after:start_time'],
            'break_start' => ['nullable', 'date_format:H:i', 'after:start_time', 'before:end_time'],
            'break_end' => ['nullable', 'date_format:H:i', 'after:break_start', 'before:end_time'],
            'location' => ['nullable', 'string', 'max:255'],
            'notes' => ['nullable', 'string', 'max:500'],
            'is_recurring' => ['boolean'],
            'recurring_pattern' => ['nullable', 'array'],
            'recurring_pattern.frequency' => [
                Rule::requiredIf($this->is_recurring),
                Rule::in(['daily', 'weekly', 'monthly'])
            ],
            'recurring_pattern.days' => [
                'nullable',
                'array',
                Rule::requiredIf($this->is_recurring && $this->input('recurring_pattern.frequency') === 'weekly')
            ],
            'recurring_pattern.days.*' => [Rule::in(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])],
            'recurring_end_date' => [
                Rule::requiredIf($this->is_recurring),
                'date',
                'after:date'
            ],
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check for schedule conflicts
            if ($this->employee_id && $this->date && $this->start_time && $this->end_time) {
                $conflictingSchedule = Schedule::where('employee_id', $this->employee_id)
                    ->where('date', $this->date)
                    ->where('status', 'active')
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            // New schedule starts during existing schedule
                            $q->where('start_time', '<=', $this->start_time)
                              ->where('end_time', '>', $this->start_time);
                        })->orWhere(function ($q) {
                            // New schedule ends during existing schedule
                            $q->where('start_time', '<', $this->end_time)
                              ->where('end_time', '>=', $this->end_time);
                        })->orWhere(function ($q) {
                            // New schedule encompasses existing schedule
                            $q->where('start_time', '>=', $this->start_time)
                              ->where('end_time', '<=', $this->end_time);
                        });
                    })
                    ->exists();

                if ($conflictingSchedule) {
                    $validator->errors()->add('start_time', 'This schedule conflicts with an existing schedule for the same employee.');
                }
            }

            // Validate break times
            if ($this->break_start && !$this->break_end) {
                $validator->errors()->add('break_end', 'Break end time is required when break start time is specified.');
            }

            if ($this->break_end && !$this->break_start) {
                $validator->errors()->add('break_start', 'Break start time is required when break end time is specified.');
            }

            // Validate shift duration (not too long)
            if ($this->start_time && $this->end_time) {
                $start = Carbon::createFromFormat('H:i', $this->start_time);
                $end = Carbon::createFromFormat('H:i', $this->end_time);
                
                // Handle overnight shifts
                if ($end->lt($start)) {
                    $end->addDay();
                }
                
                $duration = $start->diffInHours($end);
                
                if ($duration > 16) {
                    $validator->errors()->add('end_time', 'Shift duration cannot exceed 16 hours.');
                }
            }

            // Validate recurring pattern
            if ($this->is_recurring) {
                if (!$this->recurring_pattern || !is_array($this->recurring_pattern)) {
                    $validator->errors()->add('recurring_pattern', 'Recurring pattern is required for recurring schedules.');
                }

                if ($this->recurring_end_date) {
                    $startDate = Carbon::parse($this->date);
                    $endDate = Carbon::parse($this->recurring_end_date);
                    
                    if ($endDate->diffInDays($startDate) > 365) {
                        $validator->errors()->add('recurring_end_date', 'Recurring schedules cannot span more than 1 year.');
                    }
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'employee_id.required' => 'Please select an employee.',
            'employee_id.exists' => 'The selected employee is invalid.',
            'date.required' => 'Schedule date is required.',
            'date.after_or_equal' => 'Schedule cannot be created for past dates.',
            'shift_type.required' => 'Please select a shift type.',
            'start_time.required' => 'Start time is required.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.required' => 'End time is required.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'end_time.after' => 'End time must be after start time.',
            'break_start.date_format' => 'Break start time must be in HH:MM format.',
            'break_start.after' => 'Break start time must be after shift start time.',
            'break_start.before' => 'Break start time must be before shift end time.',
            'break_end.date_format' => 'Break end time must be in HH:MM format.',
            'break_end.after' => 'Break end time must be after break start time.',
            'break_end.before' => 'Break end time must be before shift end time.',
            'recurring_end_date.required' => 'End date is required for recurring schedules.',
            'recurring_end_date.after' => 'Recurring end date must be after the start date.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee',
            'shift_type' => 'shift type',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'break_start' => 'break start time',
            'break_end' => 'break end time',
            'is_recurring' => 'recurring schedule',
            'recurring_pattern' => 'recurring pattern',
            'recurring_end_date' => 'recurring end date',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default status
        $this->merge([
            'status' => 'active'
        ]);

        // Convert is_recurring to boolean
        if ($this->has('is_recurring')) {
            $this->merge([
                'is_recurring' => filter_var($this->is_recurring, FILTER_VALIDATE_BOOLEAN)
            ]);
        }

        // If not recurring, remove recurring fields
        if (!$this->is_recurring) {
            $this->request->remove('recurring_pattern');
            $this->request->remove('recurring_end_date');
        }
    }
}
