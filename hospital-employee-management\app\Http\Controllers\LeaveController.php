<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLeaveRequest;
use App\Models\Leave;
use App\Models\LeaveType;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LeaveController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('view leaves');

        $query = Leave::with(['employee.user', 'employee.department', 'leaveType', 'approver'])
            ->when($request->search, function ($q) use ($request) {
                $q->whereHas('employee', function ($query) use ($request) {
                    $query->where('first_name', 'like', "%{$request->search}%")
                          ->orWhere('last_name', 'like', "%{$request->search}%")
                          ->orWhere('employee_id', 'like', "%{$request->search}%");
                });
            })
            ->when($request->status, function ($q) use ($request) {
                $q->where('status', $request->status);
            })
            ->when($request->leave_type, function ($q) use ($request) {
                $q->where('leave_type_id', $request->leave_type);
            })
            ->when($request->department, function ($q) use ($request) {
                $q->whereHas('employee', function ($query) use ($request) {
                    $query->where('department_id', $request->department);
                });
            })
            ->when($request->date_from, function ($q) use ($request) {
                $q->where('start_date', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($q) use ($request) {
                $q->where('end_date', '<=', $request->date_to);
            });

        // Apply department-based filtering for department heads
        if ($request->user()->isDepartmentHead() && !$request->user()->can('view leaves')) {
            $query->whereHas('employee', function ($q) use ($request) {
                $q->where('department_id', $request->user()->employee->department_id);
            });
        }

        // Show only own leaves for regular employees
        if ($request->user()->isEmployee() && !$request->user()->can('view leaves')) {
            $query->where('employee_id', $request->user()->employee->id);
        }

        $leaves = $query->latest()->paginate(15);

        $leaveTypes = LeaveType::active()->get();
        $departments = \App\Models\Department::active()->get();

        return view('leaves.index', compact('leaves', 'leaveTypes', 'departments'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('request leave');

        $leaveTypes = LeaveType::active()->get();
        $employees = collect();

        // Only HR and managers can create leaves for others
        if (auth()->user()->can('create leaves')) {
            $employees = Employee::active()->with('department')->get();
        }

        return view('leaves.create', compact('leaveTypes', 'employees'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreLeaveRequest $request)
    {
        $validatedData = $request->validated();

        // Handle document upload
        if ($request->hasFile('document')) {
            $validatedData['document_path'] = $request->file('document')->store('leave-documents', 'public');
        }

        // Set status based on leave type approval requirement
        $leaveType = LeaveType::find($validatedData['leave_type_id']);
        $validatedData['status'] = $leaveType->requires_approval ? 'pending' : 'approved';

        // Auto-approve if user has permission and it's their own leave
        if (!$leaveType->requires_approval || 
            (auth()->user()->can('approve leaves') && $validatedData['employee_id'] === auth()->user()->employee->id)) {
            $validatedData['status'] = 'approved';
            $validatedData['approved_by'] = auth()->user()->employee->id;
            $validatedData['approved_at'] = now();
        }

        $leave = Leave::create($validatedData);

        $message = $leave->status === 'approved' ? 
            'Leave request submitted and approved.' : 
            'Leave request submitted successfully. Awaiting approval.';

        return redirect()->route('leaves.show', $leave)
                        ->with('success', $message);
    }

    /**
     * Display the specified resource.
     */
    public function show(Leave $leave)
    {
        $this->authorize('view own leaves');

        // Check if user can view this specific leave
        if (!auth()->user()->can('view leaves') && 
            auth()->user()->employee->id !== $leave->employee_id &&
            auth()->user()->employee->department_id !== $leave->employee->department_id) {
            abort(403, 'You can only view leaves in your department.');
        }

        $leave->load(['employee.user', 'employee.department', 'leaveType', 'approver']);

        return view('leaves.show', compact('leave'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Leave $leave)
    {
        // Only allow editing pending leaves
        if ($leave->status !== 'pending') {
            return back()->with('error', 'Only pending leave requests can be edited.');
        }

        // Check permissions
        if (!auth()->user()->can('edit leaves') && 
            auth()->user()->employee->id !== $leave->employee_id) {
            abort(403, 'You can only edit your own leave requests.');
        }

        $leaveTypes = LeaveType::active()->get();

        return view('leaves.edit', compact('leave', 'leaveTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StoreLeaveRequest $request, Leave $leave)
    {
        // Only allow updating pending leaves
        if ($leave->status !== 'pending') {
            return back()->with('error', 'Only pending leave requests can be updated.');
        }

        $validatedData = $request->validated();

        // Handle document upload
        if ($request->hasFile('document')) {
            // Delete old document
            if ($leave->document_path && Storage::disk('public')->exists($leave->document_path)) {
                Storage::disk('public')->delete($leave->document_path);
            }
            
            $validatedData['document_path'] = $request->file('document')->store('leave-documents', 'public');
        }

        $leave->update($validatedData);

        return redirect()->route('leaves.show', $leave)
                        ->with('success', 'Leave request updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Leave $leave)
    {
        // Only allow deleting pending leaves
        if ($leave->status !== 'pending') {
            return back()->with('error', 'Only pending leave requests can be deleted.');
        }

        // Check permissions
        if (!auth()->user()->can('delete leaves') && 
            auth()->user()->employee->id !== $leave->employee_id) {
            abort(403, 'You can only delete your own leave requests.');
        }

        // Delete document if exists
        if ($leave->document_path && Storage::disk('public')->exists($leave->document_path)) {
            Storage::disk('public')->delete($leave->document_path);
        }

        $leave->delete();

        return redirect()->route('leaves.index')
                        ->with('success', 'Leave request deleted successfully.');
    }

    /**
     * Approve leave request
     */
    public function approve(Leave $leave)
    {
        $this->authorize('approve leaves');

        if ($leave->status !== 'pending') {
            return back()->with('error', 'Only pending leave requests can be approved.');
        }

        $leave->update([
            'status' => 'approved',
            'approved_by' => auth()->user()->employee->id,
            'approved_at' => now(),
        ]);

        return back()->with('success', 'Leave request approved successfully.');
    }

    /**
     * Reject leave request
     */
    public function reject(Request $request, Leave $leave)
    {
        $this->authorize('reject leaves');

        if ($leave->status !== 'pending') {
            return back()->with('error', 'Only pending leave requests can be rejected.');
        }

        $request->validate([
            'rejected_reason' => 'required|string|max:500'
        ]);

        $leave->update([
            'status' => 'rejected',
            'rejected_reason' => $request->rejected_reason,
            'approved_by' => auth()->user()->employee->id,
            'approved_at' => now(),
        ]);

        return back()->with('success', 'Leave request rejected.');
    }

    /**
     * Cancel leave request
     */
    public function cancel(Leave $leave)
    {
        // Check if leave can be cancelled
        if (!$leave->canBeCancelled()) {
            return back()->with('error', 'This leave request cannot be cancelled.');
        }

        // Check permissions
        if (!auth()->user()->can('edit leaves') && 
            auth()->user()->employee->id !== $leave->employee_id) {
            abort(403, 'You can only cancel your own leave requests.');
        }

        $leave->update(['status' => 'cancelled']);

        return back()->with('success', 'Leave request cancelled successfully.');
    }

    /**
     * Download leave document
     */
    public function downloadDocument(Leave $leave)
    {
        if (!$leave->document_path || !Storage::disk('public')->exists($leave->document_path)) {
            abort(404, 'Document not found.');
        }

        // Check permissions
        if (!auth()->user()->can('view leaves') && 
            auth()->user()->employee->id !== $leave->employee_id &&
            auth()->user()->employee->department_id !== $leave->employee->department_id) {
            abort(403, 'You cannot access this document.');
        }

        return Storage::disk('public')->download($leave->document_path);
    }
}
