<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Employee Management
            'view employees',
            'create employees',
            'edit employees',
            'delete employees',
            'view employee profiles',
            'edit employee profiles',
            
            // Department Management
            'view departments',
            'create departments',
            'edit departments',
            'delete departments',
            
            // Position Management
            'view positions',
            'create positions',
            'edit positions',
            'delete positions',
            
            // Schedule Management
            'view schedules',
            'create schedules',
            'edit schedules',
            'delete schedules',
            'view own schedule',
            'edit own schedule',
            
            // Attendance Management
            'view attendance',
            'create attendance',
            'edit attendance',
            'delete attendance',
            'view own attendance',
            'clock in/out',
            'approve attendance',
            
            // Leave Management
            'view leaves',
            'create leaves',
            'edit leaves',
            'delete leaves',
            'view own leaves',
            'request leave',
            'approve leaves',
            'reject leaves',
            
            // Leave Types Management
            'view leave types',
            'create leave types',
            'edit leave types',
            'delete leave types',
            
            // Payroll Management
            'view payroll',
            'create payroll',
            'edit payroll',
            'delete payroll',
            'view own payroll',
            'process payroll',
            
            // Evaluation Management
            'view evaluations',
            'create evaluations',
            'edit evaluations',
            'delete evaluations',
            'view own evaluations',
            'conduct evaluations',
            
            // Document Management
            'view documents',
            'upload documents',
            'edit documents',
            'delete documents',
            'view own documents',
            'verify documents',
            
            // Dashboard and Reports
            'view dashboard',
            'view reports',
            'export reports',
            
            // System Administration
            'manage users',
            'manage roles',
            'manage permissions',
            'view system logs',
            'manage system settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        
        // Super Admin - Full access
        $superAdmin = Role::create(['name' => 'Super Admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // HR Manager - HR operations and employee management
        $hrManager = Role::create(['name' => 'HR Manager']);
        $hrManager->givePermissionTo([
            // Employee Management
            'view employees',
            'create employees',
            'edit employees',
            'delete employees',
            'view employee profiles',
            'edit employee profiles',
            
            // Department Management
            'view departments',
            'create departments',
            'edit departments',
            'delete departments',
            
            // Position Management
            'view positions',
            'create positions',
            'edit positions',
            'delete positions',
            
            // Schedule Management
            'view schedules',
            'create schedules',
            'edit schedules',
            'delete schedules',
            
            // Attendance Management
            'view attendance',
            'create attendance',
            'edit attendance',
            'approve attendance',
            
            // Leave Management
            'view leaves',
            'create leaves',
            'edit leaves',
            'approve leaves',
            'reject leaves',
            
            // Leave Types Management
            'view leave types',
            'create leave types',
            'edit leave types',
            'delete leave types',
            
            // Payroll Management
            'view payroll',
            'create payroll',
            'edit payroll',
            'process payroll',
            
            // Evaluation Management
            'view evaluations',
            'create evaluations',
            'edit evaluations',
            'conduct evaluations',
            
            // Document Management
            'view documents',
            'upload documents',
            'edit documents',
            'delete documents',
            'verify documents',
            
            // Dashboard and Reports
            'view dashboard',
            'view reports',
            'export reports',
            
            // User Management
            'manage users',
        ]);

        // Department Head - Department-specific management
        $departmentHead = Role::create(['name' => 'Department Head']);
        $departmentHead->givePermissionTo([
            // Employee Management (department only)
            'view employees',
            'view employee profiles',
            
            // Department Management (own department)
            'view departments',
            'edit departments',
            
            // Schedule Management (department)
            'view schedules',
            'create schedules',
            'edit schedules',
            
            // Attendance Management (department)
            'view attendance',
            'approve attendance',
            
            // Leave Management (department)
            'view leaves',
            'approve leaves',
            'reject leaves',
            
            // Evaluation Management (department)
            'view evaluations',
            'create evaluations',
            'edit evaluations',
            'conduct evaluations',
            
            // Own access
            'view own schedule',
            'view own attendance',
            'view own leaves',
            'request leave',
            'view own evaluations',
            'view own documents',
            'view own payroll',
            'clock in/out',
            
            // Dashboard
            'view dashboard',
            'view reports',
        ]);

        // Employee - Basic employee access
        $employee = Role::create(['name' => 'Employee']);
        $employee->givePermissionTo([
            // Own data access
            'view own schedule',
            'view own attendance',
            'view own leaves',
            'request leave',
            'view own evaluations',
            'view own documents',
            'view own payroll',
            
            // Basic operations
            'clock in/out',
            'view employee profiles', // own profile
            
            // Dashboard
            'view dashboard',
        ]);

        $this->command->info('Roles and permissions created successfully!');
    }
}
