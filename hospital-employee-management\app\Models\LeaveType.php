<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LeaveType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'max_days_per_year',
        'requires_approval',
        'requires_document',
        'is_paid',
        'is_active',
        'color',
    ];

    protected $casts = [
        'max_days_per_year' => 'integer',
        'requires_approval' => 'boolean',
        'requires_document' => 'boolean',
        'is_paid' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get all leave requests of this type
     */
    public function leaves(): HasMany
    {
        return $this->hasMany(Leave::class);
    }

    /**
     * Scope to get only active leave types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get leave types that require approval
     */
    public function scopeRequiresApproval($query)
    {
        return $query->where('requires_approval', true);
    }

    /**
     * Scope to get paid leave types
     */
    public function scopePaid($query)
    {
        return $query->where('is_paid', true);
    }

    /**
     * Scope to get unpaid leave types
     */
    public function scopeUnpaid($query)
    {
        return $query->where('is_paid', false);
    }

    /**
     * Check if this leave type can be deleted
     */
    public function canBeDeleted(): bool
    {
        return $this->leaves()->count() === 0;
    }

    /**
     * Get the total number of leave requests for this type
     */
    public function getLeaveRequestCountAttribute(): int
    {
        return $this->leaves()->count();
    }

    /**
     * Get approved leave requests for this type
     */
    public function getApprovedLeaveCountAttribute(): int
    {
        return $this->leaves()->where('status', 'approved')->count();
    }
}
